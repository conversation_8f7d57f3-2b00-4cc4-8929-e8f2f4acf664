{% extends "auctions/layout.html" %}
{% load static %}

{% block title %}Log In - Mazadi{% endblock %}

{% block body %}
<!-- Page Header -->
<div class="bg-gradient-to-r from-primary-600 to-primary-800 py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-3xl font-extrabold text-white sm:text-4xl">
                Log In to Your Account
            </h1>
            <p class="mt-3 max-w-2xl mx-auto text-xl text-primary-100">
                Welcome back! Log in to access your auctions and bids
            </p>
        </div>
    </div>
</div>

<div class="max-w-md mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <div class="bg-white rounded-lg shadow-md overflow-hidden animate-fade-in">
        <div class="p-8">
            <div class="mb-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-2">Log In</h2>
                <p class="text-gray-600">Enter your credentials to access your account</p>
            </div>

            <!-- Login Form -->
            <form action="{% url 'login' %}" method="post" class="space-y-6">
                {% csrf_token %}
                
                <!-- Username Field -->
                <div>
                    <label for="username" class="block text-sm font-medium text-gray-700 mb-1">
                        Username
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-user text-gray-400"></i>
                        </div>
                        <input
                            id="username"
                            name="username"
                            type="text"
                            required
                            class="w-full px-3 py-2 pl-10 border border-gray-300 rounded text-gray-700 focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300"
                            placeholder="Enter your username">
                    </div>
                </div>

                <!-- Password Field -->
                <div>
                    <label for="password" class="block text-sm font-medium text-gray-700 mb-1">
                        Password
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-lock text-gray-400"></i>
                        </div>
                        <input
                            id="password"
                            name="password"
                            type="password"
                            required
                            class="w-full px-3 py-2 pl-10 border border-gray-300 rounded text-gray-700 focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300"
                            placeholder="Enter your password">
                    </div>
                </div>

                <!-- Submit Button -->
                <div>
                    <button type="submit" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-all duration-300">
                        Log In
                    </button>
                </div>
            </form>

            <!-- Register Link -->
            <div class="mt-6 text-center">
                <p class="text-sm text-gray-600">
                    Don't have an account?
                    <a href="{% url 'register' %}" class="font-medium text-primary-600 hover:text-primary-500 transition-colors duration-300">
                        Register
                    </a>
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
