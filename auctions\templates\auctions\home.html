{% extends "auctions/layout.html" %}
{% load static %}

{% block extra_head %}
<script>
    // Add hover animation for cards
    document.addEventListener('DOMContentLoaded', function() {
        const cards = document.querySelectorAll('.animate-slide-in');
        cards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.zIndex = '10';
            });
            card.addEventListener('mouseleave', function() {
                this.style.zIndex = '1';
            });
        });
    });
</script>
{% endblock %}

{% block title %}Mazadi - Online Auction Platform{% endblock %}

{% block body %}
<!-- Hero Section -->
<section class="relative overflow-hidden">
    <!-- Hero Background with animated gradient -->
    <div class="absolute inset-0 bg-gradient-to-r from-primary-700 via-primary-600 to-primary-800 opacity-95"></div>

    <!-- Animated background patterns -->
    <div class="absolute inset-0 opacity-10">
        <div class="absolute top-0 left-0 w-full h-full bg-white">
            <svg class="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
                <defs>
                    <radialGradient id="radialGradient" cx="50%" cy="50%" r="50%" fx="50%" fy="50%">
                        <stop offset="0%" stop-color="white" stop-opacity="0.3" />
                        <stop offset="100%" stop-color="white" stop-opacity="0" />
                    </radialGradient>
                </defs>
                <circle cx="20" cy="20" r="10" fill="url(#radialGradient)" class="animate-pulse-slow" />
                <circle cx="80" cy="30" r="15" fill="url(#radialGradient)" class="animate-pulse-slow" style="animation-delay: 1s;" />
                <circle cx="40" cy="70" r="20" fill="url(#radialGradient)" class="animate-pulse-slow" style="animation-delay: 2s;" />
                <circle cx="70" cy="80" r="12" fill="url(#radialGradient)" class="animate-pulse-slow" style="animation-delay: 0.5s;" />
            </svg>
        </div>
    </div>

    <!-- Hero Content -->
    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 md:py-32">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
            <div class="text-white animate-fade-in">
                <h1 class="text-4xl md:text-5xl font-extrabold tracking-tight mb-4 leading-tight">
                    <span class="block">Discover Unique</span>
                    <span class="block text-transparent bg-clip-text bg-gradient-to-r from-white to-blue-200">Items at Mazadi</span>
                </h1>
                <p class="text-xl md:text-2xl text-blue-100 mb-8 opacity-90">
                    Bid, win, and collect extraordinary items from sellers around the world.
                </p>
                <div class="flex flex-col sm:flex-row gap-4">
                    <a href="{% url 'index' %}" class="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-primary-700 bg-white hover:bg-gray-50 transition-all duration-300 transform hover:scale-105 hover:shadow-lg group">
                        <i class="fas fa-search mr-2 transition-transform duration-300 group-hover:rotate-12"></i>
                        Browse Auctions
                    </a>
                    {% if not user.is_authenticated %}
                    <a href="{% url 'register' %}" class="inline-flex items-center justify-center px-6 py-3 border border-white text-base font-medium rounded-md text-white hover:bg-white hover:text-primary-700 transition-all duration-300 transform hover:scale-105 hover:shadow-lg group">
                        <i class="fas fa-user-plus mr-2 transition-transform duration-300 group-hover:rotate-12"></i>
                        Join Now
                    </a>
                    {% else %}
                    <a href="{% url 'create' %}" class="inline-flex items-center justify-center px-6 py-3 border border-white text-base font-medium rounded-md text-white hover:bg-white hover:text-primary-700 transition-all duration-300 transform hover:scale-105 hover:shadow-lg group">
                        <i class="fas fa-plus-circle mr-2 transition-transform duration-300 group-hover:rotate-12"></i>
                        Create Listing
                    </a>
                    {% endif %}
                </div>
            </div>
            <div class="hidden md:block relative">
                <!-- Enhanced Decorative elements with advanced animations -->
                <div class="absolute -bottom-4 -right-4 w-24 h-24 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-lg opacity-80 animate-bounce-slow transform hover:scale-110 hover:rotate-12 transition-all duration-500 shadow-lg hover:shadow-2xl"></div>
                <div class="absolute -top-4 -left-4 w-16 h-16 bg-gradient-to-br from-primary-300 to-primary-500 rounded-full opacity-80 animate-pulse-slow transform hover:scale-125 hover:rotate-45 transition-all duration-700 shadow-lg hover:shadow-2xl"></div>

                <!-- Additional decorative shapes for visual interest -->
                <div class="absolute top-1/3 -right-8 w-12 h-12 bg-gradient-to-br from-pink-400 to-red-500 rounded-full opacity-60 animate-pulse transform hover:scale-150 transition-all duration-500" style="animation-delay: 1.5s;"></div>
                <div class="absolute bottom-1/3 -left-6 w-8 h-8 bg-gradient-to-br from-green-400 to-blue-500 rounded-lg opacity-70 animate-bounce transform hover:rotate-45 transition-all duration-400" style="animation-delay: 2.5s;"></div>

                <!-- Enhanced Floating badges with modern animations -->
                <div class="absolute top-6 right-6 bg-white rounded-full px-4 py-2 shadow-lg transform rotate-3 animate-pulse-slow hover:rotate-6 hover:scale-110 hover:shadow-2xl transition-all duration-500 cursor-pointer group">
                    <span class="text-primary-700 font-bold flex items-center group-hover:text-primary-800 transition-colors duration-300">
                        <i class="fas fa-gavel mr-2 group-hover:animate-bounce group-hover:text-red-500 transition-all duration-300"></i>
                        <span class="group-hover:tracking-wide transition-all duration-300">Live Auctions</span>
                    </span>
                    <!-- Glowing effect on hover -->
                    <div class="absolute inset-0 rounded-full bg-gradient-to-r from-primary-400 to-primary-600 opacity-0 group-hover:opacity-20 transition-opacity duration-500"></div>
                </div>

                <div class="absolute bottom-6 left-6 bg-white rounded-full px-4 py-2 shadow-lg transform -rotate-3 animate-pulse-slow hover:-rotate-6 hover:scale-110 hover:shadow-2xl transition-all duration-500 cursor-pointer group" style="animation-delay: 1s;">
                    <span class="text-primary-700 font-bold flex items-center group-hover:text-primary-800 transition-colors duration-300">
                        <i class="fas fa-gem mr-2 group-hover:animate-spin group-hover:text-purple-500 transition-all duration-300"></i>
                        <span class="group-hover:tracking-wide transition-all duration-300">Unique Finds</span>
                    </span>
                    <!-- Glowing effect on hover -->
                    <div class="absolute inset-0 rounded-full bg-gradient-to-r from-purple-400 to-pink-600 opacity-0 group-hover:opacity-20 transition-opacity duration-500"></div>
                </div>

                <!-- New floating info badges -->
                <div class="absolute top-1/2 left-0 transform -translate-x-1/2 bg-gradient-to-r from-green-400 to-blue-500 text-white px-3 py-1 rounded-full text-sm font-semibold shadow-lg animate-pulse cursor-pointer hover:scale-110 transition-all duration-300" style="animation-delay: 3s;">
                    <i class="fas fa-fire mr-1"></i> Hot Deals
                </div>

                <div class="absolute top-1/4 right-0 transform translate-x-1/2 bg-gradient-to-r from-purple-500 to-pink-500 text-white px-3 py-1 rounded-full text-sm font-semibold shadow-lg animate-pulse cursor-pointer hover:scale-110 transition-all duration-300" style="animation-delay: 4s;">
                    <i class="fas fa-clock mr-1"></i> 24/7
                </div>

                <!-- Additional floating particles for extra magic -->
                <div class="absolute top-1/4 left-1/4 w-3 h-3 bg-yellow-300 rounded-full opacity-60 animate-ping" style="animation-delay: 2s; animation-duration: 3s;"></div>
                <div class="absolute top-3/4 right-1/3 w-2 h-2 bg-primary-400 rounded-full opacity-70 animate-ping" style="animation-delay: 4s; animation-duration: 4s;"></div>
                <div class="absolute top-1/2 right-1/4 w-4 h-4 bg-purple-300 rounded-full opacity-50 animate-ping" style="animation-delay: 6s; animation-duration: 5s;"></div>

                <!-- More scattered particles -->
                <div class="absolute top-1/6 left-1/2 w-2 h-2 bg-green-300 rounded-full opacity-50 animate-ping" style="animation-delay: 1s; animation-duration: 4s;"></div>
                <div class="absolute bottom-1/4 left-1/3 w-3 h-3 bg-pink-300 rounded-full opacity-40 animate-ping" style="animation-delay: 5s; animation-duration: 6s;"></div>
                <div class="absolute top-2/3 left-1/6 w-2 h-2 bg-blue-300 rounded-full opacity-60 animate-ping" style="animation-delay: 3s; animation-duration: 5s;"></div>

                <!-- Floating geometric shapes -->
                <div class="absolute top-1/5 right-1/5 w-6 h-6 border-2 border-yellow-400 rotate-45 opacity-40 animate-spin" style="animation-duration: 8s; animation-delay: 2s;"></div>
                <div class="absolute bottom-1/5 right-2/3 w-4 h-4 border-2 border-purple-400 opacity-50 animate-pulse" style="animation-delay: 4s;"></div>
            </div>
        </div>
    </div>
</section>

<!-- Featured Categories Section -->
<section class="py-20 bg-gradient-to-b from-white to-gray-50 relative overflow-hidden">
    <!-- Decorative elements -->
    <div class="absolute top-0 left-1/4 w-64 h-64 bg-primary-50 rounded-full opacity-50 blur-3xl"></div>
    <div class="absolute bottom-0 right-1/4 w-64 h-64 bg-yellow-50 rounded-full opacity-50 blur-3xl"></div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        <div class="text-center mb-16">
            <span class="inline-block px-3 py-1 bg-primary-100 text-primary-800 rounded-full text-sm font-semibold mb-3">Discover</span>
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Popular Categories</h2>
            <p class="mt-4 text-xl text-gray-600 max-w-3xl mx-auto">Explore our most popular auction categories and find exactly what you're looking for</p>
        </div>

        <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
            {% for category in categories %}
            <div class="animate-slide-in" style="animation-delay: {{ forloop.counter0 }}00ms">
                <a href="{% url 'page' category.category %}" class="block group">
                    <div class="bg-white rounded-xl shadow-md overflow-hidden aspect-square transform transition-all duration-500 group-hover:-translate-y-2 group-hover:shadow-xl relative">
                        <!-- Category icon/image with enhanced styling -->
                        <div class="h-full w-full bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center p-6 transition-all duration-500 group-hover:from-primary-50 group-hover:to-primary-100">
                            <img src="{% static 'auctions/'|add:category.category|add:'.png' %}"
                                alt="{{ category.category }}"
                                class="w-full h-full object-contain transition-all duration-500 transform group-hover:scale-110">

                            <!-- Overlay on hover -->
                            <div class="absolute inset-0 bg-primary-600 bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-300 pointer-events-none"></div>
                        </div>
                    </div>
                    <div class="mt-4 text-center group-hover:translate-y-[-2px] transition-all duration-300">
                        <h3 class="text-lg font-medium text-gray-900 group-hover:text-primary-600 transition-colors duration-300">{{ category.category }}</h3>
                        <div class="mt-1 h-0.5 w-8 bg-primary-200 mx-auto rounded-full transform origin-left transition-all duration-300 group-hover:w-16 group-hover:bg-primary-500"></div>
                    </div>
                </a>
            </div>
            {% empty %}
            <div class="col-span-full text-center py-12 bg-white rounded-xl shadow-sm">
                <div class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gray-100 mb-4">
                    <i class="fas fa-folder-open text-gray-400 text-2xl"></i>
                </div>
                <p class="text-gray-500">No categories available yet.</p>
            </div>
            {% endfor %}
        </div>

        <div class="text-center mt-16">
            <a href="{% url 'categories' %}" class="inline-flex items-center justify-center px-8 py-4 border border-transparent text-base font-medium rounded-lg text-white bg-primary-600 hover:bg-primary-700 transition-all duration-300 transform hover:scale-105 hover:shadow-lg group">
                <span>View All Categories</span>
                <i class="fas fa-arrow-right ml-2 transform transition-transform duration-300 group-hover:translate-x-1"></i>
            </a>
        </div>
    </div>
</section>

<!-- Featured Auctions Section -->
<section class="py-20 bg-white relative overflow-hidden">
    <!-- Decorative elements -->
    <div class="absolute top-0 right-1/4 w-64 h-64 bg-yellow-50 rounded-full opacity-50 blur-3xl"></div>
    <div class="absolute bottom-0 left-1/4 w-64 h-64 bg-primary-50 rounded-full opacity-50 blur-3xl"></div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        <div class="text-center mb-16">
            <span class="inline-block px-3 py-1 bg-yellow-100 text-yellow-800 rounded-full text-sm font-semibold mb-3">Hot Items</span>
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Featured Auctions</h2>
            <p class="mt-4 text-xl text-gray-600 max-w-3xl mx-auto">Discover our most exciting auctions with the highest bids right now</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            {% for auction in featured_auctions %}
            <div class="animate-slide-in" style="animation-delay: {{ forloop.counter0 }}00ms">
                <div class="bg-white rounded-lg shadow-md overflow-hidden transition-all duration-500 transform hover:-translate-y-2 hover:shadow-xl h-full flex flex-col group">
                    <!-- Image container with enhanced hover effects -->
                    <div class="h-64 overflow-hidden relative">
                        <!-- Featured badge -->
                        <div class="absolute top-3 left-3 z-10">
                            <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 shadow-sm transform transition-transform duration-300 group-hover:scale-110">
                                <i class="fas fa-star mr-1"></i> Featured
                            </span>
                        </div>

                        <!-- Status badge -->
                        <div class="absolute top-3 right-3 z-10">
                            {% if auction.is_close %}
                                <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 shadow-sm transform transition-transform duration-300 group-hover:scale-110">
                                    <i class="fas fa-times-circle mr-1"></i> Closed
                                </span>
                            {% else %}
                                <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 shadow-sm transform transition-transform duration-300 group-hover:scale-110">
                                    <i class="fas fa-check-circle mr-1"></i> Active
                                </span>
                            {% endif %}
                        </div>

                        <!-- Category badge -->
                        <div class="absolute bottom-3 left-3 z-10">
                            <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-white bg-opacity-90 text-primary-700 shadow-sm transform transition-transform duration-300 group-hover:scale-110">
                                <i class="fas fa-tag mr-1"></i> {{ auction.category }}
                            </span>
                        </div>

                        <!-- Image with enhanced hover effect -->
                        {% if auction.image %}
                            <img
                                src="{{ auction.image.url }}"
                                class="w-full h-full object-contain transition-all duration-700 ease-in-out transform group-hover:scale-110 filter group-hover:brightness-105"
                                alt="{{ auction.title }}"
                                loading="lazy"
                            >
                        {% elif auction.image_url %}
                            <img
                                src="{{ auction.image_url }}"
                                class="w-full h-full object-contain transition-all duration-700 ease-in-out transform group-hover:scale-110 filter group-hover:brightness-105"
                                alt="{{ auction.title }}"
                                loading="lazy"
                            >
                        {% else %}
                            <div class="w-full h-full bg-gray-200 flex items-center justify-center">
                                <i class="fas fa-image text-gray-400 text-4xl"></i>
                            </div>
                        {% endif %}

                        <!-- Overlay gradient on hover -->
                        <div class="absolute inset-0 bg-gradient-to-t from-black to-transparent opacity-0 group-hover:opacity-50 transition-opacity duration-300"></div>
                    </div>

                    <!-- Content with enhanced styling -->
                    <div class="p-6 flex flex-col flex-grow">
                        <div class="mb-3">
                            <h3 class="text-lg font-semibold text-gray-900 group-hover:text-primary-600 transition-colors duration-300">{{ auction.title }}</h3>
                            <p class="text-gray-600 mt-2 line-clamp-2">{{ auction.description }}</p>
                        </div>

                        <div class="mt-auto">
                            <!-- Price with animation -->
                            <div class="flex justify-end mb-4">
                                <div class="bg-primary-50 px-3 py-1 rounded-full">
                                    <span class="text-lg font-bold text-primary-700 group-hover:text-primary-800 transition-colors duration-300 inline-block transform group-hover:scale-110 transition-transform">
                                        EGP{{ auction.price }}
                                    </span>
                                </div>
                            </div>

                            <!-- CTA Button with enhanced hover effect -->
                            <a href="{% url 'auction' auction.id %}" class="relative overflow-hidden block w-full text-center px-4 py-3 border-2 border-primary-600 text-primary-600 rounded-md bg-transparent group-hover:bg-primary-600 group-hover:text-white transition-all duration-300 group-hover:shadow-lg group-hover:border-primary-700 transform group-hover:scale-105">
                                <span class="relative z-10 flex items-center justify-center font-medium">
                                    <span>View Details</span>
                                    <i class="fas fa-arrow-right ml-2 transform transition-transform duration-300 group-hover:translate-x-1"></i>
                                </span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            {% empty %}
            <div class="col-span-full text-center py-8">
                <p class="text-gray-500">No auctions available yet.</p>
            </div>
            {% endfor %}
        </div>

        <div class="text-center mt-16">
            <a href="{% url 'index' %}" class="inline-flex items-center justify-center px-8 py-4 border border-transparent text-base font-medium rounded-lg text-white bg-primary-600 hover:bg-primary-700 transition-all duration-300 transform hover:scale-105 hover:shadow-lg group">
                <span>View All Auctions</span>
                <i class="fas fa-arrow-right ml-2 transform transition-transform duration-300 group-hover:translate-x-1"></i>
            </a>
        </div>
    </div>
</section>

<!-- How It Works Section -->
<section class="py-20 bg-gradient-to-b from-gray-50 to-gray-100 relative overflow-hidden">
    <!-- Decorative elements -->
    <div class="absolute top-0 right-0 w-64 h-64 bg-primary-100 rounded-full opacity-70 transform translate-x-1/2 -translate-y-1/2"></div>
    <div class="absolute bottom-0 left-0 w-48 h-48 bg-yellow-100 rounded-full opacity-70 transform -translate-x-1/2 translate-y-1/2"></div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        <div class="text-center mb-16">
            <span class="inline-block px-3 py-1 bg-primary-100 text-primary-800 rounded-full text-sm font-semibold mb-3">Simple Process</span>
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">How It Works</h2>
            <p class="mt-4 text-xl text-gray-600 max-w-3xl mx-auto">Simple steps to start bidding and winning on Mazadi</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <!-- Step 1 -->
            <div class="bg-white p-8 rounded-xl shadow-lg text-center transform transition-all duration-300 hover:-translate-y-2 hover:shadow-xl group animate-fade-in" style="animation-delay: 100ms;">
                <div class="w-20 h-20 bg-gradient-to-br from-primary-100 to-primary-200 text-primary-600 rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-6 transform transition-all duration-500 group-hover:scale-110 group-hover:rotate-3 group-hover:bg-primary-200">
                    <i class="fas fa-search text-2xl"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-3 group-hover:text-primary-600 transition-colors duration-300">Browse Auctions</h3>
                <p class="text-gray-600">Explore our wide range of auctions across various categories to find unique items.</p>
                <div class="mt-6 h-1 w-16 bg-primary-200 mx-auto rounded-full transform origin-left transition-all duration-300 group-hover:w-24 group-hover:bg-primary-500"></div>
            </div>

            <!-- Step 2 -->
            <div class="bg-white p-8 rounded-xl shadow-lg text-center transform transition-all duration-300 hover:-translate-y-2 hover:shadow-xl group animate-fade-in" style="animation-delay: 200ms;">
                <div class="w-20 h-20 bg-gradient-to-br from-primary-100 to-primary-200 text-primary-600 rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-6 transform transition-all duration-500 group-hover:scale-110 group-hover:rotate-3 group-hover:bg-primary-200">
                    <i class="fas fa-gavel text-2xl"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-3 group-hover:text-primary-600 transition-colors duration-300">Place Your Bid</h3>
                <p class="text-gray-600">Find something you like? Place a bid that beats the current highest offer.</p>
                <div class="mt-6 h-1 w-16 bg-primary-200 mx-auto rounded-full transform origin-left transition-all duration-300 group-hover:w-24 group-hover:bg-primary-500"></div>
            </div>

            <!-- Step 3 -->
            <div class="bg-white p-8 rounded-xl shadow-lg text-center transform transition-all duration-300 hover:-translate-y-2 hover:shadow-xl group animate-fade-in" style="animation-delay: 300ms;">
                <div class="w-20 h-20 bg-gradient-to-br from-primary-100 to-primary-200 text-primary-600 rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-6 transform transition-all duration-500 group-hover:scale-110 group-hover:rotate-3 group-hover:bg-primary-200">
                    <i class="fas fa-trophy text-2xl"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-3 group-hover:text-primary-600 transition-colors duration-300">Win & Collect</h3>
                <p class="text-gray-600">If your bid wins, you'll be notified and can arrange for collection or delivery.</p>
                <div class="mt-6 h-1 w-16 bg-primary-200 mx-auto rounded-full transform origin-left transition-all duration-300 group-hover:w-24 group-hover:bg-primary-500"></div>
            </div>
        </div>
    </div>
</section>

<!-- Call to Action Section -->
<section class="py-20 bg-gradient-to-r from-primary-700 to-primary-800 relative overflow-hidden">
    <!-- Decorative elements -->
    <div class="absolute inset-0 opacity-10">
        <svg class="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
            <path d="M0,0 L100,0 L100,100 L0,100 Z" fill="none" stroke="white" stroke-width="0.5"></path>
            <path d="M0,0 L100,100" fill="none" stroke="white" stroke-width="0.5"></path>
            <path d="M100,0 L0,100" fill="none" stroke="white" stroke-width="0.5"></path>
            <circle cx="50" cy="50" r="30" fill="none" stroke="white" stroke-width="0.5" class="animate-pulse-slow"></circle>
            <circle cx="50" cy="50" r="20" fill="none" stroke="white" stroke-width="0.5" class="animate-pulse-slow" style="animation-delay: 1s;"></circle>
        </svg>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative">
        <div class="max-w-3xl mx-auto">
            <span class="inline-block px-4 py-1 bg-white bg-opacity-20 text-white rounded-full text-sm font-semibold mb-4 animate-pulse-slow">Limited Time Offers</span>
            <h2 class="text-3xl md:text-4xl font-bold text-white mb-4 leading-tight">Ready to Discover Unique Items and Amazing Deals?</h2>
            <p class="text-xl text-white text-opacity-90 mb-10">Join thousands of users finding extraordinary items every day on Mazadi</p>

            <div class="flex flex-col sm:flex-row gap-6 justify-center">
                <a href="{% url 'index' %}" class="inline-flex items-center justify-center px-8 py-4 border-2 border-transparent text-base font-medium rounded-lg text-primary-700 bg-white hover:bg-gray-50 transition-all duration-300 transform hover:scale-105 hover:shadow-lg group">
                    <i class="fas fa-search mr-2 transition-transform duration-300 group-hover:rotate-12"></i>
                    Browse Auctions
                </a>
                {% if not user.is_authenticated %}
                <a href="{% url 'register' %}" class="inline-flex items-center justify-center px-8 py-4 border-2 border-white text-base font-medium rounded-lg text-white hover:bg-white hover:text-primary-700 transition-all duration-300 transform hover:scale-105 hover:shadow-lg group">
                    <i class="fas fa-user-plus mr-2 transition-transform duration-300 group-hover:rotate-12"></i>
                    Create Account
                </a>
                {% else %}
                <a href="{% url 'create' %}" class="inline-flex items-center justify-center px-8 py-4 border-2 border-white text-base font-medium rounded-lg text-white hover:bg-white hover:text-primary-700 transition-all duration-300 transform hover:scale-105 hover:shadow-lg group">
                    <i class="fas fa-plus-circle mr-2 transition-transform duration-300 group-hover:rotate-12"></i>
                    Create Listing
                </a>
                {% endif %}
            </div>

            <!-- Floating elements -->
            <div class="hidden md:block">
                <div class="absolute top-0 left-0 transform -translate-x-1/2 -translate-y-1/2">
                    <div class="w-20 h-20 bg-yellow-400 bg-opacity-20 rounded-full animate-pulse-slow"></div>
                </div>
                <div class="absolute bottom-0 right-0 transform translate-x-1/2 translate-y-1/2">
                    <div class="w-32 h-32 bg-primary-300 bg-opacity-20 rounded-full animate-pulse-slow" style="animation-delay: 1.5s;"></div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}
