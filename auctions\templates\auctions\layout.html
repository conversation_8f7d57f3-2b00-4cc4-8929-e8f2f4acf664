{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Mazadi - Online Auction Platform{% endblock %}</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eef2ff',
                            100: '#e0e7ff',
                            200: '#c7d2fe',
                            300: '#a5b4fc',
                            400: '#818cf8',
                            500: '#6366f1',
                            600: '#4f46e5',
                            700: '#4338ca',
                            800: '#3730a3',
                            900: '#312e81',
                            950: '#1e1b4b',
                        },
                        secondary: {
                            50: '#f8fafc',
                            100: '#f1f5f9',
                            200: '#e2e8f0',
                            300: '#cbd5e1',
                            400: '#94a3b8',
                            500: '#64748b',
                            600: '#475569',
                            700: '#334155',
                            800: '#1e293b',
                            900: '#0f172a',
                            950: '#020617',
                        }
                    },
                    fontFamily: {
                        sans: ['Inter', 'sans-serif'],
                    },
                    boxShadow: {
                        'custom': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
                        'custom-lg': '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                        'slide-in': 'slideIn 0.5s ease-out',
                        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                        'bounce-slow': 'bounce 2s infinite',
                        'spin-slow': 'spin 3s linear infinite',
                        'ping-slow': 'ping 3s cubic-bezier(0, 0, 0.2, 1) infinite',
                        'pulse-custom': 'pulseCustom 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' },
                        },
                        slideIn: {
                            '0%': { transform: 'translateY(20px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' },
                        },
                        pulseCustom: {
                            '0%, 100%': { transform: 'scale(1)', opacity: '1' },
                            '50%': { transform: 'scale(1.05)', opacity: '0.8' },
                        },
                    }
                }
            }
        }
    </script>

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="{% static 'auctions/styles.css' %}" rel="stylesheet">

    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Extra head content -->
    {% block extra_head %}{% endblock %}
</head>

<body class="bg-gray-50 font-sans antialiased text-gray-900 min-h-screen flex flex-col">
    <!-- Header -->
    <header class="bg-white shadow-sm sticky top-0 z-50">
        <div x-data="{
            mobileMenuOpen: false,
            userMenuOpen: false,
            toggleMobileMenu() {
                this.mobileMenuOpen = !this.mobileMenuOpen;
                if (this.mobileMenuOpen) {
                    document.body.style.overflow = 'hidden';
                    document.body.style.position = 'fixed';
                    document.body.style.width = '100%';
                } else {
                    document.body.style.overflow = '';
                    document.body.style.position = '';
                    document.body.style.width = '';
                }
            }
        }"
        @keydown.escape="if (mobileMenuOpen) { toggleMobileMenu() }"
        class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <!-- Logo and Main Navigation -->
                <div class="flex items-center">
                    <!-- Logo -->
                    <div class="flex-shrink-0 flex items-center">
                        <a href="{% url 'home' %}" class="flex items-center">
                            <span class="text-2xl font-bold text-primary-600 mr-1">Mazadi</span>
                            <img src="{% static 'auctions/logo.png' %}" alt="Mazadi Logo" class="h-8">
                        </a>
                    </div>

                    <!-- Desktop Navigation -->
                    <nav class="hidden md:ml-8 md:flex md:space-x-6">
                        <a href="{% url 'index' %}" class="text-gray-600 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium transition duration-300">
                            <i class="fas fa-list-ul mr-1"></i> Auctions
                        </a>
                        <a href="{% url 'categories' %}" class="text-gray-600 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium transition duration-300">
                            <i class="fas fa-tags mr-1"></i> Categories
                        </a>
                        {% if user.is_authenticated %}
                            <a href="{% url 'watchlist' %}" class="text-gray-600 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium transition duration-300">
                                <i class="fas fa-heart mr-1"></i> Watchlist
                            </a>
                            <a href="{% url 'create' %}" class="text-gray-600 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium transition duration-300">
                                <i class="fas fa-plus-circle mr-1"></i> Create Listing
                            </a>
                        {% endif %}
                    </nav>
                </div>

                <!-- User Navigation -->
                <div class="flex items-center">
                    <!-- Desktop User Menu -->
                    <div class="hidden md:flex md:items-center space-x-4">
                        {% if user.is_authenticated %}
                            <!-- Notification Bell -->
                            <div class="relative" x-data="{ notificationsOpen: false, unreadCount: 0 }" x-init="
                                // Load initial unread count
                                fetch('{% url 'notification_dropdown' %}')
                                    .then(response => response.text())
                                    .then(html => {
                                        const parser = new DOMParser();
                                        const doc = parser.parseFromString(html, 'text/html');
                                        const countElement = doc.querySelector('#unread-count');
                                        if (countElement) {
                                            unreadCount = parseInt(countElement.textContent);
                                        }
                                    });
                            ">
                                <button @click="notificationsOpen = !notificationsOpen" class="relative p-1 rounded-full text-gray-600 hover:text-primary-600 focus:outline-none transition duration-300">
                                    <span class="sr-only">View notifications</span>
                                    <i class="fas fa-bell text-lg"></i>
                                    {% if unread_notification_count > 0 %}
                                        <span class="absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-red-500 rounded-full">
                                            {{ unread_notification_count }}
                                        </span>
                                    {% endif %}
                                </button>

                                <div x-show="notificationsOpen"
                                     @click.away="notificationsOpen = false"
                                     x-transition:enter="transition ease-out duration-100"
                                     x-transition:enter-start="transform opacity-0 scale-95"
                                     x-transition:enter-end="transform opacity-100 scale-100"
                                     x-transition:leave="transition ease-in duration-75"
                                     x-transition:leave-start="transform opacity-100 scale-100"
                                     x-transition:leave-end="transform opacity-0 scale-95"
                                     class="absolute right-0 mt-2 w-80 bg-white rounded-md shadow-lg py-1 z-50"
                                     x-init="
                                        $watch('notificationsOpen', value => {
                                            if (value) {
                                                fetch('{% url 'notification_dropdown' %}')
                                                    .then(response => response.text())
                                                    .then(html => {
                                                        $refs.notificationContent.innerHTML = html;
                                                        const countElement = $refs.notificationContent.querySelector('#unread-count');
                                                        if (countElement) {
                                                            unreadCount = parseInt(countElement.textContent);
                                                        }
                                                    });
                                            }
                                        })
                                     ">
                                    <div x-ref="notificationContent">
                                        <div class="py-3 px-4 text-center">
                                            <div class="inline-block animate-spin-slow">
                                                <i class="fas fa-circle-notch text-primary-500"></i>
                                            </div>
                                            <p class="text-sm text-gray-500 mt-1">Loading notifications...</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- User Menu -->
                            <div class="relative" x-data="{ open: false }">
                                <button @click="open = !open" class="flex items-center text-sm font-medium text-gray-700 hover:text-primary-600 focus:outline-none transition duration-300">
                                    <span class="mr-2">{{ user.username }}</span>

                                    {% if user.profile.profile_picture %}
                                        <img src="{{ user.profile.profile_picture.url }}" alt="{{ user.username }}" class="h-8 w-8 rounded-full object-cover border border-primary-300">
                                    {% else %}
                                        <i class="fas fa-user-circle text-lg"></i>
                                    {% endif %}
                                    <i class="fas fa-chevron-down ml-1 text-xs"></i>
                                </button>

                                <div x-show="open"
                                     @click.away="open = false"
                                     x-transition:enter="transition ease-out duration-100"
                                     x-transition:enter-start="transform opacity-0 scale-95"
                                     x-transition:enter-end="transform opacity-100 scale-100"
                                     x-transition:leave="transition ease-in duration-75"
                                     x-transition:leave-start="transform opacity-100 scale-100"
                                     x-transition:leave-end="transform opacity-0 scale-95"
                                     class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50">
                                    <a href="{% url 'profile' %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition duration-300">
                                        <i class="fas fa-user-circle mr-2"></i> My Profile
                                    </a>
                                    <a href="{% url 'notification_list' %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition duration-300">
                                        <i class="fas fa-bell mr-2"></i> Notifications
                                    </a>
                                    <div class="border-t border-gray-100 my-1"></div>
                                    <a href="{% url 'logout' %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition duration-300">
                                        <i class="fas fa-sign-out-alt mr-2"></i> Log Out
                                    </a>
                                </div>
                            </div>
                        {% else %}
                            <a href="{% url 'login' %}" class="text-gray-600 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium transition duration-300">
                                <i class="fas fa-sign-in-alt mr-1"></i> Log In
                            </a>
                            <a href="{% url 'register' %}" class="ml-4 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition duration-300">
                                <i class="fas fa-user-plus mr-1"></i> Register
                            </a>
                        {% endif %}
                    </div>

                    <!-- Enhanced Mobile menu button -->
                    <div class="flex md:hidden">
                        <button @click="toggleMobileMenu()" type="button" class="relative inline-flex items-center justify-center p-3 rounded-xl text-gray-600 hover:text-primary-600 hover:bg-primary-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-all duration-300 transform hover:scale-105 group" aria-expanded="false">
                            <span class="sr-only">Open main menu</span>
                            <!-- Animated hamburger icon -->
                            <div class="relative w-6 h-6 flex flex-col justify-center items-center">
                                <span class="absolute w-6 h-0.5 bg-current transform transition-all duration-300 ease-in-out"
                                      :class="mobileMenuOpen ? 'rotate-45 translate-y-0' : '-translate-y-2'"></span>
                                <span class="absolute w-6 h-0.5 bg-current transform transition-all duration-300 ease-in-out"
                                      :class="mobileMenuOpen ? 'opacity-0' : 'opacity-100'"></span>
                                <span class="absolute w-6 h-0.5 bg-current transform transition-all duration-300 ease-in-out"
                                      :class="mobileMenuOpen ? '-rotate-45 translate-y-0' : 'translate-y-2'"></span>
                            </div>
                            <!-- Notification badge for mobile -->
                            {% if user.is_authenticated and unread_notification_count > 0 %}
                                <span class="absolute -top-1 -right-1 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform bg-red-500 rounded-full animate-pulse">
                                    {{ unread_notification_count }}
                                </span>
                            {% endif %}
                        </button>
                    </div>
                </div>
            </div>

            <!-- Mobile menu backdrop -->
            <div x-show="mobileMenuOpen"
                 x-transition:enter="transition ease-out duration-200"
                 x-transition:enter-start="opacity-0"
                 x-transition:enter-end="opacity-100"
                 x-transition:leave="transition ease-in duration-150"
                 x-transition:leave-start="opacity-100"
                 x-transition:leave-end="opacity-0"
                 @click="toggleMobileMenu()"
                 class="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
                 style="display: none;">
            </div>

            <!-- Enhanced Mobile menu -->
            <div x-show="mobileMenuOpen"
                 x-transition:enter="transition ease-out duration-200 transform"
                 x-transition:enter-start="opacity-0 -translate-y-4"
                 x-transition:enter-end="opacity-100 translate-y-0"
                 x-transition:leave="transition ease-in duration-150 transform"
                 x-transition:leave-start="opacity-100 translate-y-0"
                 x-transition:leave-end="opacity-0 -translate-y-4"
                 class="absolute left-0 right-0 top-full bg-white border-t border-gray-100 shadow-2xl z-50 md:hidden max-h-screen overflow-y-auto"
                 style="display: none;"
                 @click.away="toggleMobileMenu()">

                <!-- Mobile Navigation Links -->
                <div class="px-4 pt-4 pb-3 space-y-2">
                    <div class="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3 px-2">Navigation</div>

                    <a href="{% url 'index' %}" @click="toggleMobileMenu()" class="group flex items-center px-4 py-3 rounded-xl text-base font-medium text-gray-700 hover:text-primary-600 hover:bg-gradient-to-r hover:from-primary-50 hover:to-primary-100 transition-all duration-300 transform hover:scale-[1.02]">
                        <div class="flex items-center justify-center w-10 h-10 rounded-lg bg-blue-100 text-blue-600 group-hover:bg-blue-200 transition-colors duration-300 mr-3">
                            <i class="fas fa-list-ul"></i>
                        </div>
                        <div class="flex-1">
                            <div class="font-medium">Auctions</div>
                            <div class="text-sm text-gray-500">Browse all auctions</div>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400 group-hover:text-primary-600 transition-colors duration-300"></i>
                    </a>

                    <a href="{% url 'categories' %}" @click="toggleMobileMenu()" class="group flex items-center px-4 py-3 rounded-xl text-base font-medium text-gray-700 hover:text-primary-600 hover:bg-gradient-to-r hover:from-primary-50 hover:to-primary-100 transition-all duration-300 transform hover:scale-[1.02]">
                        <div class="flex items-center justify-center w-10 h-10 rounded-lg bg-green-100 text-green-600 group-hover:bg-green-200 transition-colors duration-300 mr-3">
                            <i class="fas fa-tags"></i>
                        </div>
                        <div class="flex-1">
                            <div class="font-medium">Categories</div>
                            <div class="text-sm text-gray-500">Explore by category</div>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400 group-hover:text-primary-600 transition-colors duration-300"></i>
                    </a>

                    {% if user.is_authenticated %}
                        <a href="{% url 'watchlist' %}" @click="toggleMobileMenu()" class="group flex items-center px-4 py-3 rounded-xl text-base font-medium text-gray-700 hover:text-primary-600 hover:bg-gradient-to-r hover:from-primary-50 hover:to-primary-100 transition-all duration-300 transform hover:scale-[1.02]">
                            <div class="flex items-center justify-center w-10 h-10 rounded-lg bg-red-100 text-red-600 group-hover:bg-red-200 transition-colors duration-300 mr-3">
                                <i class="fas fa-heart"></i>
                            </div>
                            <div class="flex-1">
                                <div class="font-medium">Watchlist</div>
                                <div class="text-sm text-gray-500">Your saved items</div>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400 group-hover:text-primary-600 transition-colors duration-300"></i>
                        </a>

                        <a href="{% url 'create' %}" @click="toggleMobileMenu()" class="group flex items-center px-4 py-3 rounded-xl text-base font-medium text-gray-700 hover:text-primary-600 hover:bg-gradient-to-r hover:from-primary-50 hover:to-primary-100 transition-all duration-300 transform hover:scale-[1.02]">
                            <div class="flex items-center justify-center w-10 h-10 rounded-lg bg-purple-100 text-purple-600 group-hover:bg-purple-200 transition-colors duration-300 mr-3">
                                <i class="fas fa-plus-circle"></i>
                            </div>
                            <div class="flex-1">
                                <div class="font-medium">Create Listing</div>
                                <div class="text-sm text-gray-500">Sell your items</div>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400 group-hover:text-primary-600 transition-colors duration-300"></i>
                        </a>
                    {% endif %}
                </div>

                <!-- User Profile Section -->
                <div class="px-4 pt-4 pb-3 border-t border-gray-100 bg-gradient-to-r from-gray-50 to-primary-50">
                    {% if user.is_authenticated %}
                        <div class="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3 px-2">Account</div>

                        <!-- User Profile Card -->
                        <div class="bg-white rounded-xl p-4 shadow-sm border border-gray-100 mb-4">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 relative">
                                    {% if user.profile.profile_picture %}
                                        <img src="{{ user.profile.profile_picture.url }}" alt="{{ user.username }}" class="h-12 w-12 rounded-full object-cover border-2 border-primary-200 shadow-sm">
                                    {% else %}
                                        <div class="h-12 w-12 rounded-full bg-gradient-to-br from-primary-400 to-primary-600 flex items-center justify-center shadow-sm">
                                            <i class="fas fa-user text-white text-lg"></i>
                                        </div>
                                    {% endif %}
                                    <div class="absolute -bottom-1 -right-1 w-4 h-4 bg-green-400 rounded-full border-2 border-white"></div>
                                </div>
                                <div class="ml-4 flex-1">
                                    <div class="text-base font-semibold text-gray-900">{{ user.username }}</div>
                                    <div class="text-sm text-gray-500 truncate">{{ user.email }}</div>
                                    {% if unread_notification_count > 0 %}
                                        <div class="flex items-center mt-1">
                                            <div class="w-2 h-2 bg-red-400 rounded-full mr-2 animate-pulse"></div>
                                            <span class="text-xs text-red-600 font-medium">{{ unread_notification_count }} new notification{{ unread_notification_count|pluralize }}</span>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <!-- User Menu Items -->
                        <div class="space-y-2">
                            <a href="{% url 'profile' %}" @click="toggleMobileMenu()" class="group flex items-center px-4 py-3 rounded-xl text-base font-medium text-gray-700 hover:text-primary-600 hover:bg-white transition-all duration-300 transform hover:scale-[1.02]">
                                <div class="flex items-center justify-center w-10 h-10 rounded-lg bg-blue-100 text-blue-600 group-hover:bg-blue-200 transition-colors duration-300 mr-3">
                                    <i class="fas fa-user-circle"></i>
                                </div>
                                <div class="flex-1">
                                    <div class="font-medium">My Profile</div>
                                    <div class="text-sm text-gray-500">View and edit profile</div>
                                </div>
                                <i class="fas fa-chevron-right text-gray-400 group-hover:text-primary-600 transition-colors duration-300"></i>
                            </a>

                            <a href="{% url 'notification_list' %}" @click="toggleMobileMenu()" class="group flex items-center px-4 py-3 rounded-xl text-base font-medium text-gray-700 hover:text-primary-600 hover:bg-white transition-all duration-300 transform hover:scale-[1.02]">
                                <div class="flex items-center justify-center w-10 h-10 rounded-lg bg-yellow-100 text-yellow-600 group-hover:bg-yellow-200 transition-colors duration-300 mr-3 relative">
                                    <i class="fas fa-bell"></i>
                                    {% if unread_notification_count > 0 %}
                                        <span class="absolute -top-1 -right-1 inline-flex items-center justify-center px-1.5 py-0.5 text-xs font-bold leading-none text-white bg-red-500 rounded-full">
                                            {{ unread_notification_count }}
                                        </span>
                                    {% endif %}
                                </div>
                                <div class="flex-1">
                                    <div class="font-medium">Notifications</div>
                                    <div class="text-sm text-gray-500">
                                        {% if unread_notification_count > 0 %}
                                            {{ unread_notification_count }} unread message{{ unread_notification_count|pluralize }}
                                        {% else %}
                                            All caught up!
                                        {% endif %}
                                    </div>
                                </div>
                                <i class="fas fa-chevron-right text-gray-400 group-hover:text-primary-600 transition-colors duration-300"></i>
                            </a>

                            <a href="{% url 'edit_profile' %}" @click="toggleMobileMenu()" class="group flex items-center px-4 py-3 rounded-xl text-base font-medium text-gray-700 hover:text-primary-600 hover:bg-white transition-all duration-300 transform hover:scale-[1.02]">
                                <div class="flex items-center justify-center w-10 h-10 rounded-lg bg-gray-100 text-gray-600 group-hover:bg-gray-200 transition-colors duration-300 mr-3">
                                    <i class="fas fa-cog"></i>
                                </div>
                                <div class="flex-1">
                                    <div class="font-medium">Account Settings</div>
                                    <div class="text-sm text-gray-500">Manage your account</div>
                                </div>
                                <i class="fas fa-chevron-right text-gray-400 group-hover:text-primary-600 transition-colors duration-300"></i>
                            </a>

                            <a href="{% url 'payment_history' %}" @click="toggleMobileMenu()" class="group flex items-center px-4 py-3 rounded-xl text-base font-medium text-gray-700 hover:text-primary-600 hover:bg-white transition-all duration-300 transform hover:scale-[1.02]">
                                <div class="flex items-center justify-center w-10 h-10 rounded-lg bg-green-100 text-green-600 group-hover:bg-green-200 transition-colors duration-300 mr-3">
                                    <i class="fas fa-credit-card"></i>
                                </div>
                                <div class="flex-1">
                                    <div class="font-medium">My Payments</div>
                                    <div class="text-sm text-gray-500">Payment history</div>
                                </div>
                                <i class="fas fa-chevron-right text-gray-400 group-hover:text-primary-600 transition-colors duration-300"></i>
                            </a>

                            <a href="{% url 'seller_payments' %}" @click="toggleMobileMenu()" class="group flex items-center px-4 py-3 rounded-xl text-base font-medium text-gray-700 hover:text-primary-600 hover:bg-white transition-all duration-300 transform hover:scale-[1.02]">
                                <div class="flex items-center justify-center w-10 h-10 rounded-lg bg-purple-100 text-purple-600 group-hover:bg-purple-200 transition-colors duration-300 mr-3">
                                    <i class="fas fa-dollar-sign"></i>
                                </div>
                                <div class="flex-1">
                                    <div class="font-medium">Seller Payments</div>
                                    <div class="text-sm text-gray-500">Earnings & payouts</div>
                                </div>
                                <i class="fas fa-chevron-right text-gray-400 group-hover:text-primary-600 transition-colors duration-300"></i>
                            </a>

                            <!-- Logout Button -->
                            <div class="pt-3 mt-3 border-t border-gray-200">
                                <a href="{% url 'logout' %}" @click="toggleMobileMenu()" class="group flex items-center px-4 py-3 rounded-xl text-base font-medium text-red-600 hover:text-red-700 hover:bg-red-50 transition-all duration-300 transform hover:scale-[1.02]">
                                    <div class="flex items-center justify-center w-10 h-10 rounded-lg bg-red-100 text-red-600 group-hover:bg-red-200 transition-colors duration-300 mr-3">
                                        <i class="fas fa-sign-out-alt"></i>
                                    </div>
                                    <div class="flex-1">
                                        <div class="font-medium">Log Out</div>
                                        <div class="text-sm text-red-500">Sign out of your account</div>
                                    </div>
                                    <i class="fas fa-chevron-right text-red-400 group-hover:text-red-600 transition-colors duration-300"></i>
                                </a>
                            </div>
                        </div>
                    {% else %}
                        <!-- Guest User Section -->
                        <div class="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3 px-2">Get Started</div>

                        <div class="space-y-3">
                            <a href="{% url 'login' %}" @click="toggleMobileMenu()" class="group flex items-center px-4 py-4 rounded-xl text-base font-medium text-white bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 transition-all duration-300 transform hover:scale-[1.02] shadow-lg hover:shadow-xl">
                                <div class="flex items-center justify-center w-10 h-10 rounded-lg bg-white bg-opacity-20 mr-3">
                                    <i class="fas fa-sign-in-alt text-white"></i>
                                </div>
                                <div class="flex-1">
                                    <div class="font-semibold">Log In</div>
                                    <div class="text-sm text-primary-100">Access your account</div>
                                </div>
                                <i class="fas fa-chevron-right text-primary-200 group-hover:text-white transition-colors duration-300"></i>
                            </a>

                            <a href="{% url 'register' %}" @click="toggleMobileMenu()" class="group flex items-center px-4 py-4 rounded-xl text-base font-medium text-primary-700 bg-white border-2 border-primary-200 hover:border-primary-300 hover:bg-primary-50 transition-all duration-300 transform hover:scale-[1.02] shadow-sm hover:shadow-md">
                                <div class="flex items-center justify-center w-10 h-10 rounded-lg bg-primary-100 text-primary-600 group-hover:bg-primary-200 transition-colors duration-300 mr-3">
                                    <i class="fas fa-user-plus"></i>
                                </div>
                                <div class="flex-1">
                                    <div class="font-semibold">Create Account</div>
                                    <div class="text-sm text-primary-600">Join Mazadi today</div>
                                </div>
                                <i class="fas fa-chevron-right text-primary-400 group-hover:text-primary-600 transition-colors duration-300"></i>
                            </a>
                        </div>

                        <!-- Quick Info -->
                        <div class="mt-4 p-3 bg-gradient-to-r from-blue-50 to-primary-50 rounded-xl border border-blue-100">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-info-circle text-blue-500"></i>
                                </div>
                                <div class="ml-2">
                                    <p class="text-sm text-blue-700 font-medium">New to Mazadi?</p>
                                    <p class="text-xs text-blue-600">Join thousands of users buying and selling unique items!</p>
                                </div>
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </header>

    <!-- Notification Area -->
    {% if messages %}
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mt-4">
            {% for message in messages %}
                <div x-data="{ show: true }"
                     x-show="show"
                     x-transition:enter="transition ease-out duration-300"
                     x-transition:enter-start="opacity-0 transform translate-y-2"
                     x-transition:enter-end="opacity-100 transform translate-y-0"
                     x-transition:leave="transition ease-in duration-200"
                     x-transition:leave-start="opacity-100 transform translate-y-0"
                     x-transition:leave-end="opacity-0 transform translate-y-2"
                     class="rounded-md p-4 mb-4 shadow-sm {% if message.tags == 'success' %}bg-green-50 border-l-4 border-green-400 text-green-700{% elif message.tags == 'warning' %}bg-yellow-50 border-l-4 border-yellow-400 text-yellow-700{% elif message.tags == 'error' %}bg-red-50 border-l-4 border-red-400 text-red-700{% else %}bg-blue-50 border-l-4 border-blue-400 text-blue-700{% endif %}">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                {% if message.tags == 'success' %}
                                    <i class="fas fa-check-circle text-green-400"></i>
                                {% elif message.tags == 'warning' %}
                                    <i class="fas fa-exclamation-triangle text-yellow-400"></i>
                                {% elif message.tags == 'error' %}
                                    <i class="fas fa-times-circle text-red-400"></i>
                                {% else %}
                                    <i class="fas fa-info-circle text-blue-400"></i>
                                {% endif %}
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium">{{ message }}</p>
                            </div>
                        </div>
                        <button @click="show = false" class="text-gray-400 hover:text-gray-500 focus:outline-none">
                            <span class="sr-only">Close</span>
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            {% endfor %}
        </div>
    {% endif %}

    <!-- Main Content -->
    <main class="flex-grow">
        {% block body %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-white border-t border-gray-200 mt-12">
        <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <!-- Logo and Description -->
                <div class="col-span-1 md:col-span-2">
                    <div class="flex items-center">
                        <span class="text-2xl font-bold text-primary-600 mr-1">Mazadi</span>
                        <i class="fas fa-gavel text-primary-600"></i>
                    </div>
                    <p class="mt-4 text-gray-600">
                        Mazadi is an online auction platform where you can buy and sell unique items.
                        Discover amazing deals or list your own items for auction.
                    </p>
                    <div class="mt-6 flex space-x-6">
                        <a href="#" class="text-gray-400 hover:text-primary-600 transition duration-300">
                            <i class="fab fa-facebook-f text-lg"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-primary-600 transition duration-300">
                            <i class="fab fa-twitter text-lg"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-primary-600 transition duration-300">
                            <i class="fab fa-instagram text-lg"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-primary-600 transition duration-300">
                            <i class="fab fa-linkedin-in text-lg"></i>
                        </a>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h3 class="text-sm font-semibold text-gray-900 tracking-wider uppercase">Quick Links</h3>
                    <ul class="mt-4 space-y-4">
                        <li>
                            <a href="{% url 'home' %}" class="text-base text-gray-600 hover:text-primary-600 transition duration-300">
                                Home
                            </a>
                        </li>
                        <li>
                            <a href="{% url 'index' %}" class="text-base text-gray-600 hover:text-primary-600 transition duration-300">
                                Auctions
                            </a>
                        </li>
                        <li>
                            <a href="{% url 'categories' %}" class="text-base text-gray-600 hover:text-primary-600 transition duration-300">
                                Categories
                            </a>
                        </li>
                        {% if user.is_authenticated %}
                        <li>
                            <a href="{% url 'watchlist' %}" class="text-base text-gray-600 hover:text-primary-600 transition duration-300">
                                Watchlist
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </div>

                <!-- Legal -->
                <div>
                    <h3 class="text-sm font-semibold text-gray-900 tracking-wider uppercase">Legal</h3>
                    <ul class="mt-4 space-y-4">
                        <li>
                            <a href="#" class="text-base text-gray-600 hover:text-primary-600 transition duration-300">
                                Privacy Policy
                            </a>
                        </li>
                        <li>
                            <a href="#" class="text-base text-gray-600 hover:text-primary-600 transition duration-300">
                                Terms of Service
                            </a>
                        </li>
                        <li>
                            <a href="#" class="text-base text-gray-600 hover:text-primary-600 transition duration-300">
                                Cookie Policy
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="mt-12 border-t border-gray-200 pt-8">
                <p class="text-base text-gray-500 text-center">
                    &copy; 2023 Mazadi Auctions. All rights reserved.
                </p>
            </div>
        </div>
    </footer>

    <!-- Alpine.js -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <!-- Custom JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Add any custom JavaScript here

            // Smooth scrolling for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();

                    document.querySelector(this.getAttribute('href')).scrollIntoView({
                        behavior: 'smooth'
                    });
                });
            });
        });
    </script>

    <!-- Page-specific scripts -->
    {% block scripts %}{% endblock %}

    <!-- Chatbot Widget -->
    {% include 'chatbot/widget.html' %}
</body>
</html>