from django.contrib import admin
from .models import Auction, Watchlist, Bid, Comment, PriceEstimation

class AuctionAdmin(admin.ModelAdmin):
    list_display = ("id", "title", "description", "price", "estimated_price", "category", "user", "created_at")
    list_filter = ("category", "is_close", "price_estimation_date")
    search_fields = ("title", "description")
    readonly_fields = ("estimated_price", "price_estimation_date", "price_estimation_sources")


class BidAdmin(admin.ModelAdmin):
    list_display = ("id", "auction", "amount", "user")

class CommentAdmin(admin.ModelAdmin):
    list_display = ("id", "message", "user", "created_at")

class PriceEstimationAdmin(admin.ModelAdmin):
    list_display = ("id", "auction", "estimated_price", "amazon_price", "dubizzle_price", "sources_display", "created_at")
    list_filter = ("created_at", "sources_used")
    search_fields = ("auction__title", "auction__description")
    readonly_fields = ("created_at",)


# Register your models here.
admin.site.register(Auction, AuctionAdmin)
admin.site.register(Watchlist)
admin.site.register(Bid, BidAdmin)
admin.site.register(Comment, CommentAdmin)
admin.site.register(PriceEstimation, PriceEstimationAdmin)