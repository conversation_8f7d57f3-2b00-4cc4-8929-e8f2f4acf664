{% extends "auctions/layout.html" %}
{% load static %}

{% block title %}{{ notification.title }} - <PERSON><PERSON><PERSON>{% endblock %}

{% block body %}
<!-- Page Header -->
<div class="bg-gradient-to-r from-primary-600 to-primary-800 py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-3xl font-extrabold text-white sm:text-4xl">
                Notification Details
            </h1>
            <p class="mt-3 max-w-2xl mx-auto text-xl text-primary-100">
                View notification information
            </p>
        </div>
    </div>
</div>

<div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Notification Detail -->
    <div class="bg-white shadow-sm rounded-lg overflow-hidden">
        <div class="p-6">
            <!-- Notification Header -->
            <div class="flex items-center justify-between mb-6">
                <div class="flex items-center">
                    <div class="h-12 w-12 rounded-full flex items-center justify-center {{ notification.bg_color_class }}">
                        <i class="fas {{ notification.icon_class }} {{ notification.color_class }} text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <h2 class="text-xl font-bold text-gray-900">{{ notification.title }}</h2>
                        <p class="text-sm text-gray-500">{{ notification.created_at|date:"F j, Y, g:i a" }}</p>
                    </div>
                </div>
                <div class="flex space-x-2">
                    {% if notification.is_read %}
                        <form action="{% url 'mark_notification_unread' notification.id %}" method="post" class="inline">
                            {% csrf_token %}
                            <button type="submit" class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-all duration-300">
                                <i class="fas fa-envelope mr-2"></i> Mark as Unread
                            </button>
                        </form>
                    {% else %}
                        <form action="{% url 'mark_notification_read' notification.id %}" method="post" class="inline">
                            {% csrf_token %}
                            <button type="submit" class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-all duration-300">
                                <i class="fas fa-envelope-open mr-2"></i> Mark as Read
                            </button>
                        </form>
                    {% endif %}
                    <form action="{% url 'delete_notification' notification.id %}" method="post" class="inline">
                        {% csrf_token %}
                        <button type="submit" class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-all duration-300">
                            <i class="fas fa-trash-alt mr-2"></i> Delete
                        </button>
                    </form>
                </div>
            </div>
            
            <!-- Notification Content -->
            <div class="prose max-w-none text-gray-700 mb-6">
                <p>{{ notification.message }}</p>
            </div>
            
            <!-- Notification Metadata -->
            <div class="border-t border-gray-200 pt-4">
                <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
                    <div class="sm:col-span-1">
                        <dt class="text-sm font-medium text-gray-500">Type</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ notification.get_notification_type_display }}</dd>
                    </div>
                    <div class="sm:col-span-1">
                        <dt class="text-sm font-medium text-gray-500">Level</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ notification.get_level_display }}</dd>
                    </div>
                    {% if notification.link %}
                    <div class="sm:col-span-2">
                        <dt class="text-sm font-medium text-gray-500">Related Link</dt>
                        <dd class="mt-1 text-sm text-gray-900">
                            <a href="{{ notification.link }}" class="text-primary-600 hover:text-primary-800 transition-colors duration-300">
                                {{ notification.link }}
                            </a>
                        </dd>
                    </div>
                    {% endif %}
                </dl>
            </div>
            
            <!-- Action Buttons -->
            <div class="mt-6 flex justify-end">
                <a href="{% url 'notification_list' %}" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-all duration-300">
                    <i class="fas fa-arrow-left mr-2"></i> Back to Notifications
                </a>
                {% if notification.link %}
                <a href="{{ notification.link }}" class="ml-3 inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-all duration-300">
                    <i class="fas fa-external-link-alt mr-2"></i> View Related Content
                </a>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
