import requests
from bs4 import BeautifulSoup
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
import time
import re
import difflib

# ==============================
#   Similarity Function
# ==============================
def similarity_score(text1, text2):
    return difflib.SequenceMatcher(None, text1.lower(), text2.lower()).ratio()

# ==============================
#   Amazon Selenium
# ==============================
def estimate_amazon_price_selenium(name, description):
    search_query = f"{name} {description}".replace(" ", "+")
    url = f"https://www.amazon.eg/s?k={search_query}"
    print(f"[Amazon Egypt - Selenium] URL: {url}")

    options = Options()
    options.add_argument("--headless=new")
    options.add_argument("--disable-gpu")
    options.add_argument("--no-sandbox")
    options.add_argument("--disable-dev-shm-usage")
    driver = webdriver.Chrome(options=options)

    try:
        driver.get(url)
        time.sleep(1)

        prices = driver.find_elements(By.CLASS_NAME, "a-price-whole")

        for price_element in prices:
            price_text = price_element.text.replace(",", "").strip()
            if re.match(r'^\d+', price_text):
                price = float(price_text)
                driver.quit()
                if "used" in description.lower() or "مستعمل" in description:
                    return round(price * 0.8, 2)
                return round(price, 2)

        driver.quit()
        return "No Result on Amazon."
    except Exception as e:
        driver.quit()
        return f" Amazon Selenium : {str(e)}"
# ==============================
#   Dubizzle (with Similarity)
# ==============================
def estimate_dubizzle_price(name, description):
   
    if "used" not in description.lower() and "مستعمل" not in description and \
       "used" not in name.lower() and "مستعمل" not in name:
        search_terms = f"{name} {description} جديد".lower()
    else:
        search_terms = f"{name} {description}".lower()
    formatted_query = search_terms.replace(" ", "-")
    url = f"https://www.dubizzle.com.eg/ads/q-{formatted_query}/"
    print(f"[Dubizzle] URL: {url}")

    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Accept-Language": "en-US,en;q=0.9,ar;q=0.8",
    }

    try:
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
        soup = BeautifulSoup(response.text, 'html.parser')
        listings = soup.find_all('article')

        if not listings:
            return "No Result on Dubizzle."

        prices = []
        for listing in listings[:30]:
            title_element = listing.find('h2')
            price_element = listing.find('span', class_="_1f2a2b47")

            if title_element and price_element:
                title = title_element.get_text().strip()
                sim = similarity_score(title, f"{name} {description}")
                if sim >= 0.58:
                    print(f"✅ [Matched] {title} (Similarity: {round(sim, 2)})")
                    price_text = price_element.get_text().replace(",", "").strip()
                    price_match = re.search(r'[\d,]+', price_text)
                    if price_match:
                        try:
                            price = float(price_match.group().replace(",", ""))
                            prices.append(price)
                        except:
                            continue

        if not prices:
            return "No similar products on Dubizzle."

        avg_price = sum(prices) / len(prices)
        return round(avg_price, 2)

    except requests.exceptions.RequestException as e:
        return f"Dubizzle Connection Error: {str(e)}"
    except Exception as e:
        return f"Dubizzle Unexpected Error: {str(e)}"

# ==============================
#  Final Price
# ==============================
def estimate_prices(name, description):
    print("\n Start Estimation")

    amazon_price = estimate_amazon_price_selenium(name, description)
    dubizzle_price = estimate_dubizzle_price(name, description)

    print("\n--- Result ---")
    print(f"🔹 Estimated Amazon Price: {amazon_price if isinstance(amazon_price, float) else amazon_price}")
    print(f"🔹 Estimated Dubizzle Price: {dubizzle_price if isinstance(dubizzle_price, float) else dubizzle_price}")

    if isinstance(amazon_price, float) and isinstance(dubizzle_price, float):
        average_price = round((amazon_price + dubizzle_price) / 2, 2)
        print(f"\n Average Prices  : {average_price} EGP")
        return average_price
    else:
        if isinstance(amazon_price, float):
            print(f"\n Price From Amazon: {amazon_price}  EGP")
            return amazon_price
        elif isinstance(dubizzle_price, float):
            print(f"\n Price From Dubizzle: {dubizzle_price}  EGP")
            return dubizzle_price
        else:
            print("\n Not Found Result.")
            return None

# ==============================
# Test Data
# ==============================
if __name__ == "__main__":
    name = """iphone 15"""
    description = "128 جيجابايت"
    
    estimate_prices(name, description)
      



# ==============================
# requirements
# ==============================   
# pip install requests beautifulsoup4 selenium
