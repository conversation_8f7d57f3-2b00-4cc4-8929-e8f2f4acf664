{% extends "auctions/layout.html" %}
{% load static %}
{% load rating_extras %}

{% block title %}{{ profile_user.username }}'s Rat<PERSON> - <PERSON><PERSON><PERSON>{% endblock %}

{% block body %}
<!-- Page Header -->
<div class="bg-gradient-to-r from-primary-600 to-primary-800 py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-3xl font-extrabold text-white sm:text-4xl">
                {{ profile_user.username }}'s Ratings
            </h1>
            <p class="mt-3 max-w-2xl mx-auto text-xl text-primary-100">
                See what others are saying
            </p>
        </div>
    </div>
</div>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Rating Stats -->
    <div class="bg-white shadow-sm rounded-lg p-6 mb-8">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 text-center">
            <div>
                <h3 class="text-lg font-medium text-gray-500">Total Ratings</h3>
                <p class="mt-2 text-3xl font-bold text-primary-600">{{ ratings|length }}</p>
            </div>
            <div>
                <h3 class="text-lg font-medium text-gray-500">Average Rating</h3>
                <p class="mt-2 text-3xl font-bold text-primary-600">
                    {% if profile_user.profile.total_ratings_count > 0 %}
                        {% with seller_count=seller_ratings|length buyer_count=buyer_ratings|length %}
                            {% if seller_count > 0 and buyer_count > 0 %}
                                {% with total_score=profile_user.profile.seller_rating_avg|floatformat:1|multiply:seller_count|add:profile_user.profile.buyer_rating_avg|floatformat:1|multiply:buyer_count %}
                                    {% with avg_rating=total_score|divide:profile_user.profile.total_ratings_count|floatformat:1 %}
                                        {{ avg_rating }}
                                        <span class="text-yellow-500 ml-1">
                                            <i class="fas fa-star"></i>
                                        </span>
                                    {% endwith %}
                                {% endwith %}
                            {% elif seller_count > 0 %}
                                {{ profile_user.profile.seller_rating_avg|floatformat:1 }}
                                <span class="text-yellow-500 ml-1">
                                    <i class="fas fa-star"></i>
                                </span>
                            {% elif buyer_count > 0 %}
                                {{ profile_user.profile.buyer_rating_avg|floatformat:1 }}
                                <span class="text-yellow-500 ml-1">
                                    <i class="fas fa-star"></i>
                                </span>
                            {% endif %}
                        {% endwith %}
                    {% else %}
                        N/A
                    {% endif %}
                </p>
            </div>
            <div>
                <h3 class="text-lg font-medium text-gray-500">As Seller</h3>
                <p class="mt-2 text-3xl font-bold text-green-600">
                    {% if seller_ratings %}
                        {{ profile_user.profile.seller_rating_avg|floatformat:1 }}
                        <span class="text-yellow-500 ml-1">
                            <i class="fas fa-star"></i>
                        </span>
                    {% else %}
                        N/A
                    {% endif %}
                </p>
            </div>
            <div>
                <h3 class="text-lg font-medium text-gray-500">As Buyer</h3>
                <p class="mt-2 text-3xl font-bold text-blue-600">
                    {% if buyer_ratings %}
                        {{ profile_user.profile.buyer_rating_avg|floatformat:1 }}
                        <span class="text-yellow-500 ml-1">
                            <i class="fas fa-star"></i>
                        </span>
                    {% else %}
                        N/A
                    {% endif %}
                </p>
            </div>
        </div>
    </div>

    <!-- Rating Tabs -->
    <div class="mb-8">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex" aria-label="Tabs">
                <button id="all-tab" class="tab-button active w-1/3 py-4 px-1 text-center border-b-2 border-primary-500 font-medium text-sm text-primary-600">
                    All Ratings ({{ ratings|length }})
                </button>
                <button id="seller-tab" class="tab-button w-1/3 py-4 px-1 text-center border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300">
                    As Seller ({{ seller_ratings|length }})
                </button>
                <button id="buyer-tab" class="tab-button w-1/3 py-4 px-1 text-center border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300">
                    As Buyer ({{ buyer_ratings|length }})
                </button>
            </nav>
        </div>
    </div>

    <!-- Rating Lists -->
    <div id="all-ratings" class="tab-content">
        {% include 'accounts/partials/rating_list.html' with ratings=ratings %}
    </div>

    <div id="seller-ratings" class="tab-content hidden">
        {% include 'accounts/partials/rating_list.html' with ratings=seller_ratings %}
    </div>

    <div id="buyer-ratings" class="tab-content hidden">
        {% include 'accounts/partials/rating_list.html' with ratings=buyer_ratings %}
    </div>

    <!-- Back to Profile Button -->
    <div class="mt-8 text-center">
        <a href="{% url 'public_profile' profile_user.username %}" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-all duration-300">
            <i class="fas fa-arrow-left mr-2"></i>
            Back to Profile
        </a>
    </div>
</div>

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const tabButtons = document.querySelectorAll('.tab-button');
        const tabContents = document.querySelectorAll('.tab-content');

        tabButtons.forEach(button => {
            button.addEventListener('click', function() {
                // Remove active class from all buttons
                tabButtons.forEach(btn => {
                    btn.classList.remove('active', 'border-primary-500', 'text-primary-600');
                    btn.classList.add('border-transparent', 'text-gray-500');
                });

                // Add active class to clicked button
                this.classList.add('active', 'border-primary-500', 'text-primary-600');
                this.classList.remove('border-transparent', 'text-gray-500');

                // Hide all tab contents
                tabContents.forEach(content => {
                    content.classList.add('hidden');
                });

                // Show the corresponding tab content
                const tabId = this.id.replace('-tab', '-ratings');
                document.getElementById(tabId).classList.remove('hidden');
            });
        });
    });
</script>
{% endblock %}
{% endblock %}
