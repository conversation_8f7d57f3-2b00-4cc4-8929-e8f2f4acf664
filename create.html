{% extends "auctions/layout.html" %}
{% load static %}

{% block title %}Create New Auction - Ma<PERSON>i{% endblock %}

{% block body %}
<!-- Page Header -->
<div class="bg-gradient-to-r from-primary-600 to-primary-800 py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-3xl font-extrabold text-white sm:text-4xl">
                Create New Auction
            </h1>
            <p class="mt-3 max-w-2xl mx-auto text-xl text-primary-100">
                List your item and start receiving bids from buyers around the world
            </p>
        </div>
    </div>
</div>

<div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Create Auction Form -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden animate-fade-in">
        <div class="p-8">
            <div class="mb-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-2">Auction Details</h2>
                <p class="text-gray-600">Fill out the form below to create your auction listing.</p>
            </div>

            <form action="{% url 'create' %}" method="post" enctype="multipart/form-data" class="space-y-6">
                {% csrf_token %}

                <!-- Image Options Section -->
                <div class="mb-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-3">Image Options <span class="text-red-500">*</span></h3>
                    <p class="text-sm text-gray-600 mb-4">Choose one of the following options to add an image to your listing:</p>

                    <!-- Image Upload Field -->
                    <div class="mb-6">
                        <label for="{{ form.image.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            Option 1: Upload Image
                        </label>
                        <div class="mt-1">
                            <div class="flex items-center justify-center w-full">
                                <label for="{{ form.image.id_for_label }}" class="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100 transition-all duration-300">
                                    <div class="flex flex-col items-center justify-center pt-5 pb-6">
                                        <i class="fas fa-cloud-upload-alt text-gray-400 text-3xl mb-2"></i>
                                        <p class="mb-2 text-sm text-gray-500"><span class="font-semibold">Click to upload</span> or drag and drop</p>
                                        <p class="text-xs text-gray-500">PNG, JPG or JPEG (MAX. 5MB)</p>
                                    </div>
                                    <input type="file" id="{{ form.image.id_for_label }}" name="{{ form.image.html_name }}" class="hidden" accept="image/*" onchange="previewImage(this)">
                                </label>
                            </div>
                        </div>
                        <p class="mt-1 text-xs text-gray-500">Upload a high-quality image of your item.</p>

                        <!-- Image Preview -->
                        <div id="imagePreviewContainer" class="mt-4 hidden">
                            <h4 class="text-sm font-medium text-gray-700 mb-2">Image Preview:</h4>
                            <div class="relative bg-gray-100 rounded-lg overflow-hidden" style="height: 200px;">
                                <img id="imagePreview" src="#" alt="Preview" class="w-full h-full object-contain">
                                <button type="button" onclick="removeImage()" class="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors duration-300">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Image URL Field -->
                    <div>
                        <label for="{{ form.image_url.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            Option 2: Provide Image URL
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-link text-gray-400"></i>
                            </div>
                            {{ form.image_url }}
                        </div>
                        <p class="mt-1 text-xs text-gray-500">Enter a direct URL to an image on the web.</p>
                    </div>

                    <div class="mt-2 text-xs text-gray-500 bg-blue-50 p-2 rounded border border-blue-100">
                        <p class="flex items-center"><i class="fas fa-info-circle text-blue-500 mr-1"></i> You must either upload an image or provide an image URL.</p>
                    </div>
                </div>

                <!-- Title Field -->
                <div>
                    <label for="{{ form.title.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        Title <span class="text-red-500">*</span>
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-heading text-gray-400"></i>
                        </div>
                        {{ form.title }}
                    </div>
                    <p class="mt-1 text-xs text-gray-500">Create a clear, concise title that describes your item.</p>
                </div>

                <!-- Description Field -->
                <div>
                    <label for="{{ form.description.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        Description <span class="text-red-500">*</span>
                    </label>
                    {{ form.description }}
                    <p class="mt-1 text-xs text-gray-500">Provide a detailed description of your item, including condition, features, and any relevant details.</p>
                </div>

                <!-- Price Field -->
                <div>
                    <label for="{{ form.price.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        Price <span class="text-red-500">*</span>
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-1 flex items-center pointer-events-none">
                            <span class="text-gray-500">EGP</span>
                        </div>
                        {{ form.price }}
                    </div>
                    <p class="mt-1 text-xs text-gray-500">Set a reasonable price for your item.</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Category Field -->
                    <div>
                        <label for="{{ form.category.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            Category <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-tag text-gray-400"></i>
                            </div>
                            {{ form.category }}
                        </div>
                        <p class="mt-1 text-xs text-gray-500">Select the most appropriate electronics category for your item.</p>
                    </div>

                    <!-- Starting Bid Field -->
                    <div>
                        <label for="{{ form.amount.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            Starting Bid <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-gavel text-gray-400"></i>
                            </div>
                            {{ form.amount }}
                        </div>
                        <p class="mt-1 text-xs text-gray-500">Set the minimum starting bid for your auction.</p>
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="pt-6 border-t border-gray-200">
                    <div class="flex justify-end">
                        <a href="{% url 'index' %}" class="border border-secondary-600 text-secondary-600 hover:bg-secondary-600 hover:text-white font-medium py-2 px-4 rounded-md transition duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-secondary-500 mr-4">
                            Cancel
                        </a>
                        <button type="submit" class="flex items-center bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-md shadow-sm transition duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            <i class="fas fa-gavel mr-2"></i> Create Auction
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Auction Tips -->
    <div class="bg-white rounded-lg shadow-md p-6 mt-8">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Tips for a Successful Auction</h3>
        <ul class="space-y-3 text-gray-600">
            <li class="flex items-start">
                <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                <span>Use high-quality images that clearly show your item</span>
            </li>
            <li class="flex items-start">
                <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                <span>Write detailed descriptions including condition, dimensions, and features</span>
            </li>
            <li class="flex items-start">
                <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                <span>Set a reasonable starting bid to attract more bidders</span>
            </li>
            <li class="flex items-start">
                <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                <span>Choose the correct category to help buyers find your item</span>
            </li>
        </ul>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Function to preview the uploaded image
    function previewImage(input) {
        const previewContainer = document.getElementById('imagePreviewContainer');
        const preview = document.getElementById('imagePreview');

        if (input.files && input.files[0]) {
            const reader = new FileReader();

            reader.onload = function(e) {
                preview.src = e.target.result;
                previewContainer.classList.remove('hidden');

                // Also clear any URL input when an image is uploaded
                const urlInput = document.getElementById('{{ form.image_url.id_for_label }}');
                if (urlInput) {
                    urlInput.value = '';
                }
            }

            reader.readAsDataURL(input.files[0]);
        }
    }

    // Function to remove the uploaded image
    function removeImage() {
        const previewContainer = document.getElementById('imagePreviewContainer');
        const preview = document.getElementById('imagePreview');
        const fileInput = document.getElementById('{{ form.image.id_for_label }}');

        preview.src = '#';
        previewContainer.classList.add('hidden');
        fileInput.value = '';
    }

    // Function to preview image from URL
    document.addEventListener('DOMContentLoaded', function() {
        const urlInput = document.getElementById('{{ form.image_url.id_for_label }}');

        if (urlInput) {
            urlInput.addEventListener('input', function() {
                const previewContainer = document.getElementById('imagePreviewContainer');
                const preview = document.getElementById('imagePreview');
                const fileInput = document.getElementById('{{ form.image.id_for_label }}');

                if (this.value.trim() !== '') {
                    preview.src = this.value;
                    previewContainer.classList.remove('hidden');

                    // Clear file input when URL is provided
                    if (fileInput) {
                        fileInput.value = '';
                    }

                    // Handle image load error
                    preview.onerror = function() {
                        preview.src = '/static/auctions/placeholder.png';
                    };
                } else {
                    previewContainer.classList.add('hidden');
                }
            });
        }
    });
</script>



<script>
document.addEventListener("DOMContentLoaded", function () {
    const nameInput = document.getElementById("id_title");
    const descInput = document.getElementById("id_description");
    const priceInput = document.getElementById("id_price");
    const amountInput = document.getElementById("id_amount");

    // دالة لحساب 95% من السعر وتحديث حقل amount
    function updateAmount() {
        const price = parseFloat(priceInput.value);
        if (!isNaN(price)) {
            const amount = (price * 0.95).toFixed(2);
            amountInput.value = amount;
        } else {
            amountInput.value = '';
        }
    }

    // // تأخير التنفيذ لتقليل عدد الطلبات (Debounce)
    function debounce(func, delay) {
        let timer;
        return function (...args) {
            clearTimeout(timer);
            timer = setTimeout(() => func.apply(this, args), delay);
        };
    }

    const fetchPrice = debounce(() => {
        const name = nameInput.value;
        const desc = descInput.value;

        if (name.length < 3) return;

        fetch(`/api/estimate-price/?name=${encodeURIComponent(name)}&description=${encodeURIComponent(desc)}`)
            .then(response => response.json())
            .then(data => {
                if (data.estimated_price) {
                    priceInput.value = data.estimated_price;
                    updateAmount();  // 🟢 تحديث الـ amount بعد ما السعر يتحدد
                }
            })
            .catch(error => {
                console.error("Error fetching price:", error);
            });
    }, 1500);

    nameInput.addEventListener("input", fetchPrice);
    descInput.addEventListener("input", fetchPrice);
});
</script>





{% endblock %}
