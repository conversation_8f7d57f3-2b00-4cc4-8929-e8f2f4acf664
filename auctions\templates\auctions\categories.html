    {% extends "auctions/layout.html" %}
{% load static %}

{% block title %}Categories - <PERSON><PERSON><PERSON>{% endblock %}

{% block body %}
<!-- Page Header -->
<div class="bg-gradient-to-r from-primary-600 to-primary-800 py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-3xl font-extrabold text-white sm:text-4xl">
                Shop by Category
            </h1>
            <p class="mt-3 max-w-2xl mx-auto text-xl text-primary-100">
                Find exactly what you're looking for in our organized categories
            </p>
        </div>
    </div>
</div>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <!-- Categories Grid -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
        {% for category in categories %}
            <div class="animate-slide-in" style="animation-delay: {{ forloop.counter0 }}00ms">
                <a href="{% url 'page' category.category %}" class="group">
                    <div class="bg-white rounded-lg shadow-md overflow-hidden transition-all duration-300 transform hover:-translate-y-1 hover:shadow-lg h-full">
                        <div class="aspect-square bg-gray-100 overflow-hidden relative">
                            <img src="{% static 'auctions/'|add:category.category|add:'.png' %}"
                                alt="{{ category.category }}"
                                class="w-full h-full object-contain transition-all duration-300 ease-in-out p-6">

                            <!-- Overlay on hover -->
                            <div class="absolute inset-0 bg-primary-600 bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-300 flex items-center justify-center">
                                <div class="bg-white bg-opacity-0 group-hover:bg-opacity-90 transform translate-y-8 group-hover:translate-y-0 transition-all duration-300 rounded-full p-3">
                                    <i class="fas fa-arrow-right text-primary-600"></i>
                                </div>
                            </div>
                        </div>

                        <div class="p-6 text-center">
                            <h3 class="text-lg font-semibold text-gray-900 group-hover:text-primary-600 transition duration-300">{{ category.category }}</h3>
                            <p class="text-sm text-gray-500 mt-1">
                                <span class="inline-flex items-center">
                                    <i class="fas fa-tag mr-1 text-primary-500"></i>
                                    <span>{{ category.count|default:"0" }} items</span>
                                </span>
                            </p>
                        </div>
                    </div>
                </a>
            </div>
        {% empty %}
            <div class="col-span-full">
                <div class="bg-yellow-50 border-l-4 border-yellow-400 p-6 rounded-md">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-exclamation-circle text-yellow-400 text-xl"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-lg font-medium text-yellow-800">No categories found</h3>
                            <div class="mt-2 text-yellow-700">
                                <p>There are no categories available yet. Check back soon!</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>

    <!-- Category Benefits Section -->
    <div class="mt-16 bg-gray-50 rounded-xl p-8">
        <div class="text-center mb-10">
            <h2 class="text-2xl font-bold text-gray-900">Why Browse by Category?</h2>
            <p class="mt-4 text-gray-600 max-w-3xl mx-auto">
                Browsing by category helps you find exactly what you're looking for faster and discover related items you might be interested in.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div class="bg-white p-6 rounded-lg shadow-sm text-center">
                <div class="w-12 h-12 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-search"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">Find Items Faster</h3>
                <p class="text-gray-600">Narrow down your search to exactly what you're looking for.</p>
            </div>

            <div class="bg-white p-6 rounded-lg shadow-sm text-center">
                <div class="w-12 h-12 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-lightbulb"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">Discover New Items</h3>
                <p class="text-gray-600">Find related items you might not have thought to search for.</p>
            </div>

            <div class="bg-white p-6 rounded-lg shadow-sm text-center">
                <div class="w-12 h-12 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-bell"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">Stay Updated</h3>
                <p class="text-gray-600">Follow categories to get notified when new items are listed.</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}