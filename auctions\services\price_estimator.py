"""
Price estimation service for auction items.

This service provides price estimation functionality by scraping
Amazon Egypt and Dubizzle Egypt to get current market prices.
"""

import logging
import re
import time
from typing import Dict, Optional, Tuple, Union
from decimal import Decimal
import difflib

import requests
from bs4 import BeautifulSoup
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.common.exceptions import WebDriverException, TimeoutException
from webdriver_manager.chrome import ChromeDriverManager

from django.conf import settings
from django.core.cache import cache
from django.utils import timezone

logger = logging.getLogger(__name__)


class PriceEstimationError(Exception):
    """Custom exception for price estimation errors."""
    pass


class PriceEstimatorService:
    """
    Service class for estimating product prices from various sources.
    
    This service follows Django best practices:
    - Uses dependency injection
    - Implements proper error handling
    - Uses caching to avoid repeated requests
    - Follows DRY principles
    """
    
    # Cache timeout in seconds (1 hour)
    CACHE_TIMEOUT = 3600
    
    # Similarity threshold for product matching
    SIMILARITY_THRESHOLD = 0.58
    
    # Used product discount factor
    USED_DISCOUNT_FACTOR = 0.8
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept-Language': 'en-US,en;q=0.9,ar;q=0.8',
        })
    
    def estimate_price(self, name: str, description: str) -> Dict[str, Union[float, str, None]]:
        """
        Main method to estimate price from multiple sources.
        
        Args:
            name: Product name
            description: Product description
            
        Returns:
            Dictionary containing price estimates and metadata
        """
        logger.info(f"Starting price estimation for: {name}")
        
        # Check cache first
        cache_key = self._generate_cache_key(name, description)
        cached_result = cache.get(cache_key)
        if cached_result:
            logger.info(f"Returning cached result for: {name}")
            return cached_result
        
        result = {
            'amazon_price': None,
            'dubizzle_price': None,
            'average_price': None,
            'estimated_price': None,
            'sources_used': [],
            'estimation_date': timezone.now(),
            'errors': []
        }
        
        # Get Amazon price
        try:
            amazon_price = self._estimate_amazon_price(name, description)
            if isinstance(amazon_price, (int, float)):
                result['amazon_price'] = float(amazon_price)
                result['sources_used'].append('amazon')
        except Exception as e:
            logger.error(f"Amazon price estimation failed: {str(e)}")
            result['errors'].append(f"Amazon: {str(e)}")
        
        # Get Dubizzle price
        try:
            dubizzle_price = self._estimate_dubizzle_price(name, description)
            if isinstance(dubizzle_price, (int, float)):
                result['dubizzle_price'] = float(dubizzle_price)
                result['sources_used'].append('dubizzle')
        except Exception as e:
            logger.error(f"Dubizzle price estimation failed: {str(e)}")
            result['errors'].append(f"Dubizzle: {str(e)}")
        
        # Calculate final estimate
        result['estimated_price'] = self._calculate_final_estimate(
            result['amazon_price'], 
            result['dubizzle_price']
        )
        
        # Cache the result
        cache.set(cache_key, result, self.CACHE_TIMEOUT)
        
        logger.info(f"Price estimation completed for: {name}. Estimated price: {result['estimated_price']}")
        return result
    
    def _estimate_amazon_price(self, name: str, description: str) -> Union[float, str]:
        """Estimate price from Amazon Egypt using Selenium."""
        search_query = f"{name} {description}".replace(" ", "+")
        url = f"https://www.amazon.eg/s?k={search_query}"
        
        logger.debug(f"Amazon URL: {url}")
        
        driver = None
        try:
            driver = self._get_webdriver()
            driver.get(url)
            time.sleep(2)  # Wait for page to load
            
            # Find price elements
            price_elements = driver.find_elements(By.CLASS_NAME, "a-price-whole")
            
            for price_element in price_elements:
                price_text = price_element.text.replace(",", "").strip()
                if re.match(r'^\d+', price_text):
                    price = float(price_text)
                    
                    # Apply discount for used items
                    if self._is_used_item(description):
                        price *= self.USED_DISCOUNT_FACTOR
                    
                    return round(price, 2)
            
            return "No price found on Amazon"
            
        except Exception as e:
            logger.error(f"Amazon scraping error: {str(e)}")
            raise PriceEstimationError(f"Amazon estimation failed: {str(e)}")
        finally:
            if driver:
                driver.quit()
    
    def _estimate_dubizzle_price(self, name: str, description: str) -> Union[float, str]:
        """Estimate price from Dubizzle Egypt using requests."""
        # Prepare search terms
        search_terms = self._prepare_dubizzle_search_terms(name, description)
        formatted_query = search_terms.replace(" ", "-")
        url = f"https://www.dubizzle.com.eg/ads/q-{formatted_query}/"
        
        logger.debug(f"Dubizzle URL: {url}")
        
        try:
            response = self.session.get(url, timeout=10)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            listings = soup.find_all('article')
            
            if not listings:
                return "No listings found on Dubizzle"
            
            prices = []
            for listing in listings[:30]:  # Limit to first 30 listings
                price = self._extract_dubizzle_price(listing, name, description)
                if price:
                    prices.append(price)
            
            if not prices:
                return "No similar products found on Dubizzle"
            
            # Calculate average price
            average_price = sum(prices) / len(prices)
            return round(average_price, 2)
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Dubizzle request error: {str(e)}")
            raise PriceEstimationError(f"Dubizzle estimation failed: {str(e)}")
        except Exception as e:
            logger.error(f"Dubizzle parsing error: {str(e)}")
            raise PriceEstimationError(f"Dubizzle estimation failed: {str(e)}")
    
    def _extract_dubizzle_price(self, listing, name: str, description: str) -> Optional[float]:
        """Extract price from a Dubizzle listing if it matches the product."""
        title_element = listing.find('h2')
        price_element = listing.find('span', class_="_1f2a2b47")
        
        if not (title_element and price_element):
            return None
        
        title = title_element.get_text().strip()
        similarity = self._calculate_similarity(title, f"{name} {description}")
        
        if similarity >= self.SIMILARITY_THRESHOLD:
            logger.debug(f"Matched listing: {title} (Similarity: {round(similarity, 2)})")
            
            price_text = price_element.get_text().replace(",", "").strip()
            price_match = re.search(r'[\d,]+', price_text)
            
            if price_match:
                try:
                    return float(price_match.group().replace(",", ""))
                except ValueError:
                    pass
        
        return None
    
    def _get_webdriver(self) -> webdriver.Chrome:
        """Get configured Chrome WebDriver."""
        options = Options()
        options.add_argument("--headless=new")
        options.add_argument("--disable-gpu")
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--disable-blink-features=AutomationControlled")
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        
        try:
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=options)
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            return driver
        except Exception as e:
            logger.error(f"WebDriver initialization failed: {str(e)}")
            raise PriceEstimationError(f"WebDriver setup failed: {str(e)}")
    
    def _prepare_dubizzle_search_terms(self, name: str, description: str) -> str:
        """Prepare search terms for Dubizzle based on item condition."""
        if not self._is_used_item(description) and not self._is_used_item(name):
            return f"{name} {description} جديد".lower()
        return f"{name} {description}".lower()
    
    def _is_used_item(self, text: str) -> bool:
        """Check if the item is described as used."""
        text_lower = text.lower()
        return "used" in text_lower or "مستعمل" in text
    
    def _calculate_similarity(self, text1: str, text2: str) -> float:
        """Calculate similarity score between two texts."""
        return difflib.SequenceMatcher(None, text1.lower(), text2.lower()).ratio()
    
    def _calculate_final_estimate(self, amazon_price: Optional[float], dubizzle_price: Optional[float]) -> Optional[float]:
        """Calculate final price estimate from available sources."""
        if amazon_price and dubizzle_price:
            return round((amazon_price + dubizzle_price) / 2, 2)
        elif amazon_price:
            return amazon_price
        elif dubizzle_price:
            return dubizzle_price
        return None
    
    def _generate_cache_key(self, name: str, description: str) -> str:
        """Generate cache key for price estimation."""
        import hashlib
        content = f"{name}_{description}".lower()
        return f"price_estimate_{hashlib.md5(content.encode()).hexdigest()}"
