{% extends "auctions/layout.html" %}
{% load static %}

{% block title %}Security Questions - <PERSON><PERSON><PERSON>{% endblock %}

{% block body %}
<!-- Page Header -->
<div class="bg-gradient-to-r from-primary-600 to-primary-800 py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-3xl font-extrabold text-white sm:text-4xl">
                Security Questions
            </h1>
            <p class="mt-3 max-w-2xl mx-auto text-xl text-primary-100">
                Set up security questions to help recover your account
            </p>
        </div>
    </div>
</div>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
        <!-- Sidebar Navigation -->
        <div class="lg:col-span-1">
            <div class="bg-white rounded-lg shadow-sm overflow-hidden sticky top-24">
                <div class="p-6">
                    <div class="flex items-center mb-6">
                        {% if user.profile.profile_picture %}
                            <img src="{{ user.profile.profile_picture.url }}" alt="{{ user.username }}" class="h-16 w-16 rounded-full object-contain border-2 border-primary-500">
                        {% else %}
                            <div class="h-16 w-16 rounded-full bg-primary-100 flex items-center justify-center border-2 border-primary-500">
                                <i class="fas fa-user text-2xl text-primary-500"></i>
                            </div>
                        {% endif %}
                        <div class="ml-4">
                            <h2 class="text-lg font-semibold text-gray-900">{{ user.username }}</h2>
                            <p class="text-sm text-gray-500">{{ user.email }}</p>
                        </div>
                    </div>

                    <nav class="space-y-2">
                        <a href="{% url 'profile' %}" class="flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-primary-700 transition-colors duration-300">
                            <i class="fas fa-user-circle mr-3 text-gray-400 group-hover:text-primary-500"></i>
                            Profile Overview
                        </a>
                        <a href="{% url 'edit_profile' %}" class="flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-primary-700 transition-colors duration-300">
                            <i class="fas fa-edit mr-3 text-gray-400 group-hover:text-primary-500"></i>
                            Edit Profile
                        </a>
                        <a href="{% url 'change_password' %}" class="flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-primary-700 transition-colors duration-300">
                            <i class="fas fa-key mr-3 text-gray-400 group-hover:text-primary-500"></i>
                            Change Password
                        </a>
                        <a href="{% url 'security_questions' %}" class="flex items-center px-3 py-2 text-sm font-medium rounded-md bg-primary-50 text-primary-700">
                            <i class="fas fa-shield-alt mr-3 text-primary-500"></i>
                            Security Questions
                        </a>
                        <a href="{% url 'my_auctions' %}" class="flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-primary-700 transition-colors duration-300">
                            <i class="fas fa-gavel mr-3 text-gray-400 group-hover:text-primary-500"></i>
                            My Auctions
                        </a>
                        <a href="{% url 'watchlist' %}" class="flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-primary-700 transition-colors duration-300">
                            <i class="fas fa-heart mr-3 text-gray-400 group-hover:text-primary-500"></i>
                            Watchlist
                        </a>
                    </nav>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="lg:col-span-3">
            <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                <div class="p-6">
                    <h2 class="text-xl font-bold text-gray-900 mb-6">Security Questions</h2>

                    <div class="bg-blue-50 p-4 rounded-lg mb-6">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-info-circle text-blue-500"></i>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-blue-800">Why set up security questions?</h3>
                                <div class="mt-2 text-sm text-blue-700">
                                    <p>Security questions provide an additional layer of protection for your account. They can be used to verify your identity if you forget your password or if we detect unusual activity on your account.</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <form method="post" class="space-y-6">
                        {% csrf_token %}

                        <!-- Security Questions -->
                        <div class="space-y-4">
                            <div>
                                <label for="id_security_question1" class="block text-sm font-medium text-gray-700 mb-1">Security Question 1</label>
                                {{ form.security_question1 }}
                            </div>

                            <div>
                                <label for="id_security_answer1" class="block text-sm font-medium text-gray-700 mb-1">Answer</label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <i class="fas fa-reply text-gray-400"></i>
                                    </div>
                                    {{ form.security_answer1 }}
                                </div>
                                <p class="mt-1 text-xs text-gray-500">Your answer is case-sensitive. Remember it exactly as entered.</p>
                            </div>

                            <div>
                                <label for="id_security_question2" class="block text-sm font-medium text-gray-700 mb-1">Security Question 2</label>
                                {{ form.security_question2 }}
                            </div>

                            <div>
                                <label for="id_security_answer2" class="block text-sm font-medium text-gray-700 mb-1">Answer</label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <i class="fas fa-reply text-gray-400"></i>
                                    </div>
                                    {{ form.security_answer2 }}
                                </div>
                                <p class="mt-1 text-xs text-gray-500">Your answer is case-sensitive. Remember it exactly as entered.</p>
                            </div>
                        </div>

                        <!-- Security Tips -->
                        <div class="bg-yellow-50 p-4 rounded-lg">
                            <h3 class="text-sm font-medium text-yellow-800 mb-2">Tips for Security Questions</h3>
                            <ul class="text-xs text-yellow-700 space-y-1 pl-5 list-disc">
                                <li>Choose answers that are easy for you to remember but difficult for others to guess.</li>
                                <li>Avoid using information that can be found on your social media profiles.</li>
                                <li>Consider using answers that are not factually correct but memorable to you.</li>
                                <li>Make sure to remember your answers exactly as you've entered them, including capitalization.</li>
                            </ul>
                        </div>

                        <!-- Submit Button -->
                        <div class="flex justify-end">
                            <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-300">
                                <i class="fas fa-save mr-2"></i> Save Security Questions
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
