"""
Price estimation services following Django best practices.

This module provides price estimation functionality by scraping
Amazon Egypt and Dubizzle Egypt to get current market prices.
"""

import hashlib
import logging
import re
import time
from datetime import <PERSON><PERSON><PERSON>
from typing import Dict, Optional, Union
from decimal import Decimal
import difflib

import requests
from bs4 import BeautifulSoup
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.common.exceptions import WebDriverException, TimeoutException
from webdriver_manager.chrome import ChromeDriverManager

from django.conf import settings
from django.utils import timezone
from django.core.cache import cache

from .models import PriceEstimationCache

logger = logging.getLogger(__name__)


class PriceEstimationError(Exception):
    """Custom exception for price estimation errors."""
    pass


class BasePriceEstimator:
    """
    Base class for price estimators following DRY principles.
    
    This abstract base class defines the common interface and
    shared functionality for all price estimation sources.
    """
    
    def __init__(self):
        self.session = self._create_session()
    
    def _create_session(self):
        """Create and configure requests session."""
        session = requests.Session()
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept-Language': 'en-US,en;q=0.9,ar;q=0.8',
        })
        return session
    
    def estimate_price(self, name: str, description: str) -> Union[float, str]:
        """Abstract method to be implemented by subclasses."""
        raise NotImplementedError("Subclasses must implement estimate_price method")
    
    def _is_used_item(self, text: str) -> bool:
        """Check if the item is described as used."""
        text_lower = text.lower()
        return "used" in text_lower or "مستعمل" in text
    
    def _calculate_similarity(self, text1: str, text2: str) -> float:
        """Calculate similarity score between two texts."""
        return difflib.SequenceMatcher(None, text1.lower(), text2.lower()).ratio()


class AmazonPriceEstimator(BasePriceEstimator):
    """Amazon Egypt price estimator using Selenium."""
    
    USED_DISCOUNT_FACTOR = 0.8
    
    def estimate_price(self, name: str, description: str) -> Union[float, str]:
        """Estimate price from Amazon Egypt using Selenium."""
        search_query = f"{name} {description}".replace(" ", "+")
        url = f"https://www.amazon.eg/s?k={search_query}"
        
        logger.debug(f"Amazon URL: {url}")
        
        driver = None
        try:
            driver = self._get_webdriver()
            driver.get(url)
            time.sleep(2)  # Wait for page to load
            
            # Find price elements
            price_elements = driver.find_elements(By.CLASS_NAME, "a-price-whole")
            
            for price_element in price_elements:
                price_text = price_element.text.replace(",", "").strip()
                if re.match(r'^\d+', price_text):
                    price = float(price_text)
                    
                    # Apply discount for used items
                    if self._is_used_item(description):
                        price *= self.USED_DISCOUNT_FACTOR
                    
                    return round(price, 2)
            
            return "No price found on Amazon"
            
        except Exception as e:
            logger.error(f"Amazon scraping error: {str(e)}")
            raise PriceEstimationError(f"Amazon estimation failed: {str(e)}")
        finally:
            if driver:
                driver.quit()
    
    def _get_webdriver(self) -> webdriver.Chrome:
        """Get configured Chrome WebDriver."""
        options = Options()
        options.add_argument("--headless=new")
        options.add_argument("--disable-gpu")
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--disable-blink-features=AutomationControlled")
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        
        try:
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=options)
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            return driver
        except Exception as e:
            logger.error(f"WebDriver initialization failed: {str(e)}")
            raise PriceEstimationError(f"WebDriver setup failed: {str(e)}")


class DubizzlePriceEstimator(BasePriceEstimator):
    """Dubizzle Egypt price estimator using requests."""
    
    SIMILARITY_THRESHOLD = 0.58
    
    def estimate_price(self, name: str, description: str) -> Union[float, str]:
        """Estimate price from Dubizzle Egypt using requests."""
        import urllib.parse

        # Prepare search terms
        search_terms = self._prepare_search_terms(name, description)

        # Try multiple URL formats for better success rate
        urls_to_try = [
            f"https://www.dubizzle.com.eg/ads/q-{search_terms.replace(' ', '-')}/",
            f"https://www.dubizzle.com.eg/ads/q-{urllib.parse.quote(search_terms)}/",
            f"https://www.dubizzle.com.eg/search?q={urllib.parse.quote(search_terms)}",
        ]

        for url in urls_to_try:
            try:
                logger.debug(f"Trying Dubizzle URL: {url}")

                # Add headers to appear more like a real browser
                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language': 'en-US,en;q=0.9,ar;q=0.8',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'DNT': '1',
                    'Connection': 'keep-alive',
                    'Upgrade-Insecure-Requests': '1',
                }

                response = self.session.get(url, headers=headers, timeout=15)

                # Check if we got a successful response
                if response.status_code == 200:
                    soup = BeautifulSoup(response.text, 'html.parser')
                    listings = soup.find_all('article')

                    if listings:
                        prices = []
                        for listing in listings[:20]:  # Limit to first 20 listings
                            price = self._extract_price(listing, name, description)
                            if price:
                                prices.append(price)

                        if prices:
                            # Calculate average price
                            average_price = sum(prices) / len(prices)
                            logger.info(f"Found {len(prices)} matching prices on Dubizzle")
                            return round(average_price, 2)

                    # If no listings found with this URL, try the next one
                    continue
                else:
                    logger.warning(f"Dubizzle returned status {response.status_code} for URL: {url}")
                    continue

            except requests.exceptions.RequestException as e:
                logger.warning(f"Dubizzle request failed for URL {url}: {str(e)}")
                continue
            except Exception as e:
                logger.warning(f"Dubizzle parsing failed for URL {url}: {str(e)}")
                continue

        # If all URLs failed
        return "No listings found on Dubizzle (all URL formats failed)"
    
    def _prepare_search_terms(self, name: str, description: str) -> str:
        """Prepare search terms for Dubizzle based on item condition."""
        # Clean and limit the search terms
        name_clean = self._clean_search_text(name)
        description_clean = self._clean_search_text(description)

        # Extract key terms from description (brand, model, key features)
        key_terms = self._extract_key_terms(description_clean)

        # Combine name with key terms, prioritizing the most important info
        if key_terms:
            search_terms = f"{name_clean} {' '.join(key_terms[:3])}"  # Max 3 key terms
        else:
            search_terms = f"{name_clean} {description_clean[:30]}"  # Limit description

        # Limit total length to avoid URL issues
        if len(search_terms) > 80:  # Conservative limit
            search_terms = search_terms[:80].rsplit(' ', 1)[0]  # Cut at word boundary

        # Add condition indicator for new items
        if not self._is_used_item(description) and not self._is_used_item(name):
            if len(search_terms) < 70:  # Only add if there's room
                search_terms = f"{search_terms} جديد"

        return search_terms.strip().lower()

    def _extract_key_terms(self, text: str) -> list:
        """Extract key terms that are likely to be important for search."""
        import re

        # Common electronics brands and terms
        important_terms = [
            'samsung', 'apple', 'iphone', 'ipad', 'macbook', 'dell', 'hp', 'lenovo',
            'asus', 'acer', 'sony', 'lg', 'huawei', 'xiaomi', 'oppo', 'vivo',
            'gaming', 'laptop', 'desktop', 'monitor', 'keyboard', 'mouse',
            'headphones', 'speakers', 'camera', 'tablet', 'phone', 'smart',
            'wireless', 'bluetooth', 'usb', 'ssd', 'hdd', 'ram', 'gb', 'tb',
            'intel', 'amd', 'nvidia', 'core', 'ryzen', 'gtx', 'rtx'
        ]

        # Find important terms in the text
        found_terms = []
        text_lower = text.lower()

        for term in important_terms:
            if term in text_lower:
                found_terms.append(term)

        # Also extract numbers that might be model numbers or specs
        numbers = re.findall(r'\b\d+(?:gb|tb|inch|hz)?\b', text_lower)
        found_terms.extend(numbers[:2])  # Max 2 numbers

        return found_terms[:5]  # Return max 5 terms

    def _clean_search_text(self, text: str) -> str:
        """Clean text for URL-safe search terms."""
        import re

        # Remove newlines and extra whitespace
        text = re.sub(r'\s+', ' ', text.strip())

        # Remove special characters that cause URL issues
        text = re.sub(r'[^\w\s\u0600-\u06FF-]', ' ', text)  # Keep Arabic, alphanumeric, spaces, hyphens

        # Remove model numbers and technical specs that are too specific
        text = re.sub(r'\b[A-Z0-9]{5,}\b', '', text)  # Remove long alphanumeric codes
        text = re.sub(r'\[[^\]]*\]', '', text)  # Remove content in brackets
        text = re.sub(r'\([^)]*\)', '', text)  # Remove content in parentheses

        # Clean up multiple spaces
        text = re.sub(r'\s+', ' ', text).strip()

        return text
    
    def _extract_price(self, listing, name: str, description: str) -> Optional[float]:
        """Extract price from a Dubizzle listing if it matches the product."""
        title_element = listing.find('h2')
        price_element = listing.find('span', class_="_1f2a2b47")
        
        if not (title_element and price_element):
            return None
        
        title = title_element.get_text().strip()
        similarity = self._calculate_similarity(title, f"{name} {description}")
        
        if similarity >= self.SIMILARITY_THRESHOLD:
            logger.debug(f"Matched listing: {title} (Similarity: {round(similarity, 2)})")
            
            price_text = price_element.get_text().replace(",", "").strip()
            price_match = re.search(r'[\d,]+', price_text)
            
            if price_match:
                try:
                    return float(price_match.group().replace(",", ""))
                except ValueError:
                    pass
        
        return None


class PriceEstimatorService:
    """
    Main service class for price estimation following Django best practices.
    
    This service:
    - Uses dependency injection for estimators
    - Implements caching to avoid repeated requests
    - Follows DRY principles
    - Provides comprehensive error handling
    """
    
    # Cache timeout in seconds (1 hour)
    CACHE_TIMEOUT = 3600
    
    def __init__(self):
        self.estimators = {
            'amazon': AmazonPriceEstimator(),
            'dubizzle': DubizzlePriceEstimator(),
        }
    
    def estimate_price(self, name: str, description: str, use_cache: bool = True) -> Dict[str, Union[float, str, None]]:
        """
        Main method to estimate price from multiple sources.
        
        Args:
            name: Product name
            description: Product description
            use_cache: Whether to use cached results
            
        Returns:
            Dictionary containing price estimates and metadata
        """
        logger.info(f"Starting price estimation for: {name}")
        
        # Check cache first if enabled
        if use_cache:
            cached_result = self._get_cached_result(name, description)
            if cached_result:
                logger.info(f"Returning cached result for: {name}")
                return cached_result
        
        result = {
            'amazon_price': None,
            'dubizzle_price': None,
            'estimated_price': None,
            'sources_used': [],
            'estimation_date': timezone.now(),
            'errors': []
        }
        
        # Get prices from all estimators
        for source_name, estimator in self.estimators.items():
            try:
                price = estimator.estimate_price(name, description)
                if isinstance(price, (int, float)):
                    result[f'{source_name}_price'] = float(price)
                    result['sources_used'].append(source_name)
                else:
                    result['errors'].append(f"{source_name}: {price}")
            except Exception as e:
                logger.error(f"{source_name} price estimation failed: {str(e)}")
                result['errors'].append(f"{source_name}: {str(e)}")
        
        # Calculate final estimate
        result['estimated_price'] = self._calculate_final_estimate(
            result['amazon_price'], 
            result['dubizzle_price']
        )
        
        # Cache the result if caching is enabled
        if use_cache:
            self._cache_result(name, description, result)
        
        logger.info(f"Price estimation completed for: {name}. Estimated price: {result['estimated_price']}")
        return result
    
    def _calculate_final_estimate(self, amazon_price: Optional[float], dubizzle_price: Optional[float]) -> Optional[float]:
        """Calculate final price estimate from available sources."""
        if amazon_price and dubizzle_price:
            return round((amazon_price + dubizzle_price) / 2, 2)
        elif amazon_price:
            return amazon_price
        elif dubizzle_price:
            return dubizzle_price
        return None
    
    def _generate_cache_key(self, name: str, description: str) -> str:
        """Generate cache key for price estimation."""
        content = f"{name}_{description}".lower()
        return hashlib.md5(content.encode()).hexdigest()
    
    def _get_cached_result(self, name: str, description: str) -> Optional[Dict]:
        """Get cached price estimation result."""
        cache_key = self._generate_cache_key(name, description)
        
        try:
            cache_entry = PriceEstimationCache.objects.get(
                cache_key=cache_key,
                expires_at__gt=timezone.now()
            )
            
            # Increment hit count
            cache_entry.increment_hit_count()
            
            return {
                'amazon_price': float(cache_entry.amazon_price) if cache_entry.amazon_price else None,
                'dubizzle_price': float(cache_entry.dubizzle_price) if cache_entry.dubizzle_price else None,
                'estimated_price': float(cache_entry.estimated_price) if cache_entry.estimated_price else None,
                'sources_used': cache_entry.sources_used,
                'estimation_date': cache_entry.created_at,
                'errors': cache_entry.estimation_errors,
                'cached': True
            }
        except PriceEstimationCache.DoesNotExist:
            return None
    
    def _cache_result(self, name: str, description: str, result: Dict) -> None:
        """Cache price estimation result."""
        cache_key = self._generate_cache_key(name, description)
        expires_at = timezone.now() + timedelta(seconds=self.CACHE_TIMEOUT)
        
        try:
            # Update existing cache entry or create new one
            cache_entry, created = PriceEstimationCache.objects.update_or_create(
                cache_key=cache_key,
                defaults={
                    'estimated_price': Decimal(str(result['estimated_price'])) if result['estimated_price'] else None,
                    'amazon_price': Decimal(str(result['amazon_price'])) if result['amazon_price'] else None,
                    'dubizzle_price': Decimal(str(result['dubizzle_price'])) if result['dubizzle_price'] else None,
                    'sources_used': result['sources_used'],
                    'estimation_errors': result['errors'],
                    'expires_at': expires_at,
                }
            )
            
            if created:
                logger.debug(f"Created new cache entry for: {name}")
            else:
                logger.debug(f"Updated cache entry for: {name}")
                
        except Exception as e:
            logger.error(f"Failed to cache result for {name}: {str(e)}")
            # Don't raise exception for caching failures
