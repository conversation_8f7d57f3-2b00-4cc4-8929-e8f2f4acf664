{% extends "auctions/layout.html" %}
{% load static %}

{% block title %}My Auctions - Ma<PERSON>i{% endblock %}

{% block body %}
<!-- Page Header -->
<div class="bg-gradient-to-r from-primary-600 to-primary-800 py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-3xl font-extrabold text-white sm:text-4xl">
                My Auctions
            </h1>
            <p class="mt-3 max-w-2xl mx-auto text-xl text-primary-100">
                Manage your auction listings
            </p>
        </div>
    </div>
</div>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <!-- Stats -->
    <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="bg-gray-50 p-4 rounded-lg text-center">
                <p class="text-3xl font-bold text-primary-600">{{ auction_count }}</p>
                <p class="text-sm text-gray-500">Total Auctions</p>
            </div>
            <div class="bg-gray-50 p-4 rounded-lg text-center">
                <p class="text-3xl font-bold text-green-600">{{ auctions|dictsortreversed:"created_at"|slice:":1"|length }}</p>
                <p class="text-sm text-gray-500">Active Auctions</p>
            </div>
            <div class="bg-gray-50 p-4 rounded-lg text-center">
                <p class="text-3xl font-bold text-gray-600">{{ auctions|dictsortreversed:"created_at"|slice:":1"|length }}</p>
                <p class="text-sm text-gray-500">Completed Auctions</p>
            </div>
        </div>
    </div>

    <!-- Create New Auction Button -->
    <div class="flex justify-end mb-6">
        <a href="{% url 'create' %}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-all duration-300">
            <i class="fas fa-plus-circle mr-2"></i> Create New Auction
        </a>
    </div>

    <!-- Auctions Grid -->
    {% if auctions %}
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {% for auction in auctions %}
                <div class="bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden transition-all duration-300 transform hover:-translate-y-1 hover:shadow-md">
                    <div class="h-48 overflow-hidden relative">
                        {% if auction.image %}
                            <img src="{{ auction.image.url }}" alt="{{ auction.title }}" class="w-full h-full object-contain" loading="lazy">
                        {% elif auction.image_url %}
                            <img src="{{ auction.image_url }}" alt="{{ auction.title }}" class="w-full h-full object-contain" loading="lazy">
                        {% else %}
                            <div class="w-full h-full bg-gray-200 flex items-center justify-center">
                                <i class="fas fa-image text-gray-400 text-4xl"></i>
                            </div>
                        {% endif %}

                        <!-- Status Badge -->
                        <div class="absolute top-0 left-0 m-2">
                            {% if auction.is_close %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                    <i class="fas fa-check-circle mr-1"></i> Closed
                                </span>
                            {% else %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <i class="fas fa-gavel mr-1"></i> Active
                                </span>
                            {% endif %}
                        </div>

                        <!-- Category Badge -->
                        <div class="absolute top-0 right-0 m-2">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
                                {{ auction.category }}
                            </span>
                        </div>
                    </div>

                    <div class="p-4">
                        <h3 class="text-lg font-semibold text-gray-900 mb-1">
                            <a href="{% url 'auction' auction.id %}" class="hover:text-primary-600 transition-colors duration-300">{{ auction.title }}</a>
                        </h3>

                        <p class="text-sm text-gray-500 mb-2 line-clamp-2">{{ auction.description }}</p>

                        <div class="flex justify-between items-center">
                            <p class="text-lg font-bold text-primary-600">EGP{{ auction.price }}</p>
                            <p class="text-xs text-gray-500">{{ auction.created_at|date:"M d, Y" }}</p>
                        </div>

                        <!-- Action Buttons - Row 1 -->
                        <div class="mt-4 flex space-x-2 mb-2">
                            <a href="{% url 'auction' auction.id %}" class="flex-1 inline-flex justify-center items-center px-3 py-1.5 border border-gray-300 rounded text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-300">
                                <i class="fas fa-eye mr-1"></i> View
                            </a>
                        </div>

                        <!-- Action Buttons - Row 2 -->
                        <div class="flex space-x-2">
                            {% if not auction.is_close %}
                                <form action="{% url 'close' auction.id %}" method="post" class="flex-1">
                                    {% csrf_token %}
                                    <button type="submit" class="w-full inline-flex justify-center items-center px-3 py-1.5 border border-transparent rounded text-sm font-medium text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-colors duration-300">
                                        <i class="fas fa-times-circle mr-1"></i> Close
                                    </button>
                                </form>
                            {% else %}
                                <button disabled class="flex-1 inline-flex justify-center items-center px-3 py-1.5 border border-gray-200 rounded text-sm font-medium text-gray-400 bg-gray-100 cursor-not-allowed">
                                    <i class="fas fa-check-circle mr-1"></i> Closed
                                </button>
                            {% endif %}

                            <form action="{% url 'delete_auction' auction.id %}" method="post" class="flex-1" onsubmit="return confirm('Are you sure you want to delete this auction? This action cannot be undone.');">
                                {% csrf_token %}
                                <button type="submit" class="w-full inline-flex justify-center items-center px-3 py-1.5 border border-transparent rounded text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-300">
                                    <i class="fas fa-trash-alt mr-1"></i> Delete
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="bg-white rounded-lg shadow-sm p-12 text-center">
            <div class="inline-block p-6 bg-gray-100 rounded-full mb-4">
                <i class="fas fa-box-open text-gray-400 text-5xl"></i>
            </div>
            <h3 class="text-xl font-medium text-gray-900 mb-2">No Auctions Yet</h3>
            <p class="text-gray-500 mb-6">You haven't created any auctions yet. Start selling by creating your first auction.</p>
            <a href="{% url 'create' %}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-all duration-300">
                <i class="fas fa-plus-circle mr-2"></i> Create New Auction
            </a>
        </div>
    {% endif %}
</div>
{% endblock %}
